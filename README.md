# 🕵️🗂️ Iris

[![Iris Build Status](<https://teamcity.taservs.net/app/rest/builds/buildType:(id:KubernetesServices_Rms_IrisV2_2_Build)/statusIcon.svg>)](https://teamcity.taservs.net/project.html?projectId=KubernetesServices_Rms_IrisV2_2)
![Node version](https://img.shields.io/badge/node-20.10.0-brightgreen.svg)

## 🔗 Useful Links

- [CI Builds](https://teamcity.taservs.net/viewType.html?buildTypeId=KubernetesServices_Rms_IrisV2_2_Build)
- [CI Build Metrics](https://teamcity.taservs.net/project.html?projectId=KubernetesServices_Rms_IrisV2_2&tab=stats)
- [JS Bundle Analyzer](https://teamcity.taservs.net/repository/download/KubernetesServices_Rms_IrisV2_2_Build/.lastSuccessful/artifacts/records/stats.html?branch=%3Cdefault%3E)
- [Frontend Styleguide](https://axon.quip.com/M0NhAIpc7lE9/Front-End-Style-Guide-RMS)

## 👁️ Overview

This repo requires node LTS (20.x) to build and install dependencies correctly.

Iris is the JS monorepo for our Record Management System (RMS). It's currently available on AG1 at
https://sb-pro.ag1.evidence.com/records.

There are many packages, but the 2 main projects are:

- Iris (RMS FE Web Application):
  - Code is at [packages/iris](https://git.taservs.net/rcom/iris/tree/master/packages/iris)
  - Deployed version is on [AG1](https://sb-pro.ag1.evidence.com/records)
- Electra (RMS Styleguide):
  - Code is at [packages/electra](https://git.taservs.net/rcom/iris/tree/master/packages/electra)
  - Currently being migrated to Axon styleguide: [@axon-enterprise/spark](https://pages.git.taservs.net/axon/axon-ui)
    - Connect to DV1 VPN to access @axon-enterprise/spark storybook.

iris is served locally on port 3000 (`open localhost:3000`).

To run the development server, and watch changes for all projects run `pnpm start`.

## 🖥️ Setup

### Prerequisites

This project assumes you have an instance of [Hermes README](https://git.taservs.net/rcom/hermes)
located next to your iris project, and [Node](https://nodejs.org) with
[pnpm](https://pnpm.io/installation) installed.

#### _**Hermes**_

To run Iris, you must have Hermes cloned next to your Iris project. For instructions on how to setup Hermes, see the [Hermes README](https://git.taservs.net/rcom/hermes). If you run into issues, reach out to the [#rms-hermes](https://axon.slack.com/messages/CCCMT2ZA9/) slack channel for additional information.

#### _**Node**_

Install node dependencies running `pnpm install` on the project's directory.

#### _**M1 Chip MacBooks**_

For M1 MacOS troubleshooting take a look at this Quip doc [here](https://axon.quip.com/oNkNAV9vQbtE/RMS-Local-Development#temp:C:JDO93ddd08a497a49b09991a4762)

### 🔗 Start local development server

By default, Iris send request to Hermes running locally on port :8888. Open two separate terminal windows and run commands below:

- Start Hermes development server:

```bash
cd ~/hermes
# recommend running hermes with qaZeke flag to connect to services deployed on AG1.
# You can also run hermes by connecting to all dependent services running locally. See hermes readme for more details.
pnpm start qaZeke
```

- Start Iris development server:

```bash
cd ~/iris
pnpm start
```

### 🔗 Connect to Hermes on ag1

The Hermes API gateway needs to make a bunch of grpc calls to multiple services to complete a single graphql query. This is not a big deal and is usually fast. But if you are using a poor internet connection or sitting very far away from ag1 cluster, the network latency can add up to 15s for every api call from iris to hermes.
By using hermes running on ag1, you will reduce the number of requests sent from your local machine to the ag1 cluster. The average latency can be reduced to at most 2s.
You can choose one of the following three to archive connecting on ag1.

```bash
# Run on https://sb-pro.ag1.evidence.com by default
pnpm start:ag1
```

```bash
# Run with custom agency like https://singleagency.ag1.evidence.com
pnpm start:ag1 "https://singleagency.ag1.evidence.com"
```

```bash
# Run traditional way
cd ~/iris
ENDPOINT_HERMES="https://sb-pro.ag1.evidence.com" pnpm start
```

> **_Note_:** Iris is still using schema from you local hermes folder. Please make sure that it is up to date.

## 📝 Usage Notes

The production build can be built with `pnpm build`.

Build commands only generate the build and quit. To use that generated output run `pnpm watch:server`.

`pnpm build` and `pnpm start` commands will, by default, generate files from hermes.

Using node version manager (NVM) is suggested to allow running different node versions locally for different projects:

```bash
brew install nvm

nvm install 20

nvm use 20

# installs pnpm for the node 20 context, only needs to be run once
corepack enable
```

### Package Versioning

This repo uses [changesets](https://github.com/changesets/changesets) to version packages held within and pnpm to publish the packages.

#### How to Version Packages

If you make a change to the code in any of the packages in this repo, you will need to generate a changeset file.
You can do this by:

1. Running `pnpm changeset` at the root of the Iris repo
2. Select the packages that should be included in the changeset
   1. You'll like only need to select the "All changed packages" option
3. Select which packages should get a major/minor/patch version bump
   1. Just hit <kbd>Enter</kbd> without selecting any packages if none should be major/minor version bumped
4. Write a summary of the changes you made
5. Commit the generated `.changeset/*.md` file to your branch

#### Changeset Status Action

The Changeset Status action will run on your PR with four possible outcomes:

1. No packages were changed so no changeset is needed
   1. Check will pass
2. Packages were changed and there's a valid changeset file for the changes
   1. Check will pass
3. Packages were change and there is not a valid changeset file for the changes
   1. Check will fail
   2. You must generate a changeset file following the [steps above](#how-to-version-packages)
4. Your branch has too many commits since the last master commit
   1. Check will fail
   2. For performance reasons the action only checks out the 50 most recent commits. If you have more than that in your PR branch, changesets won't be able to detect the divergence point between the two branches
   3. You must rebase onto or merge latest master branch into your branch

#### Changeset Version on Master Action

When a commit is pushed to the master branch that has a changeset file in it, this action will run `pnpm changeset version` to bump the versions of all the packages according to the changeset file, update the `CHANGELOG.md` files for each package, and run `pnpm install`. These changes then get committed directly to the master branch which will get picked up by TeamCity which will eventually run the `scripts/ci/publish.sh` script to publish the new package versions to the package registry.

## Creating a local config file

When developing in Iris locally, you might need to override some of the configuration values. The easiest way to do this is to create a local config. To do this create a file in the `packages/iris/server` directory called `.env.js` and paste the template below into the file.

```js
module.exports = {
  // EXAMPLE_CONFIG_OVERRIDE_KEY: "example config override value"
}
```

The `.env.js` file is Git ignored by default. Once it's created, you can add any environment variables you wish to override into this file, without fear of committing those changes.

## Connecting to other partner agencies

When running Iris locally, the partner you are connecting to is stored in the [packages/iris/server/config.js](./packages/iris/server/config.js) under the `HOST_EDC` variable. To override this, follow the instructions above to create a local config file and add a `HOST_EDC` value for the partner you want to connect to (eg, `HOST_EDC: "standards.ag1.evidence.com"` for a typical Standards deployment).

Alternatively, you can edit the [packages/iris/server/config.js](./packages/iris/server/config.js) file directly and change `HOST_EDC` to the partner you want to connect to, but these changes should not be committed.

Then, navigate to localhost:3000. You will now be able to log in with your user credentials for that partner.

Note: This feature requires [hermes](https://git.taservs.net/rcom/hermes) to be running in `qaZeke` mode.

To use this feature with the hermes `noauth` flag, modify `HOST_EDC` as described above, then follow the hermes instructions for using `noauth` mode.

## Testing

Tests can be run with `pnpm run test`.

To skip Jest's retry behavior use `SKIP_JEST_RETRY=1`.

## 📦 Creating New Packages

To get started creating a new package for the monorepo, use `pnpm generate` to create a minimal boilerplate.

## 📓 Notes

Icons are consumed from @axon-enterprise/icon. You can view the latest icons via Storybook [here](https://git.taservs.net/pages/axon/axon-ui/?path=/story/next-components-icon--icon).

The Electra styleguide is currently in maintenance mode and being deprecated in favor of the Axon styleguide library [@axon-enterprise/spark](https://pages.git.taservs.net/axon/axon-ui). No new styleguide components should be added to Electra.

Dependencies should be added with `pnpm add [depName]` and devDependencies with `pnpm add -D [devDepName]`.
To remove them, use `pnpm remove [depName]`. All devDependencies should be added to the root of the project to
avoid duplication of dependencies.

For development, you may want to use:

[Apollo DevTools](https://github.com/apollographql/apollo-client-devtools) which provide a
[GraphiQL](https://github.com/graphql/graphiql) client and show the representation of the cached
data as a tree.

[React Devtools](https://github.com/facebook/react-devtools) are also helpful for debugging React
components.
