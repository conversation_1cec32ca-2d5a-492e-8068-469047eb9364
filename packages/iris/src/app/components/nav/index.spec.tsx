import * as React from 'react'

import * as envUtils from 'shared/envUtils'
import { fireEvent, render, screen } from 'test/utils'

import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import { moduleName } from 'shared/pageSettings'
import { ModuleEnums } from 'shared/modules'
import * as hooks from 'shared/hooks'

import { Nav } from './index'
import { mockUseUserAccess, mockUseUserContext } from '../../../../test/mocks/userContext'

// eslint-disable-next-line max-statements
describe('Nav component', () => {
  let defaultProps: React.ComponentProps<typeof Nav>

  function setup(
    props: React.ComponentProps<typeof Nav>,
    mockUserAccessOverrides = {},
    mockUserContextOverrides = {}
  ) {
    mockUseUserAccess({
      overrides: {
        viewAdminPage: false,
        viewRecordsTasks: true,
        canAccessAuditLog: false,
        canAccessMasterChargeTable: false,
        canEditMasterLocationTool: false,
        canViewAllReports: true,
        standardsAccess: true,
        canAccessMNIDedupe: false,
        ...mockUserAccessOverrides,
      },
    })

    mockUseUserContext({
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) => attr === PartnerFeatureEnums.RMSACCESS,
      },
      ...mockUserContextOverrides,
    })

    return render(<Nav {...props} />)
  }

  beforeEach(() => {
    defaultProps = {
      activeModule: moduleName.search,
    }
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)
    jest.spyOn(hooks, 'useAgencyPartnerIdFromRoute').mockReturnValue(undefined)
  })

  afterEach(() => jest.clearAllMocks())

  it('should not display AdminConsole button without permission', () => {
    setup(defaultProps)
    expect(screen.queryByRole('button', { name: /Administrator Console/i })).toBeNull()
  })

  it('should display AdminConsole button with viewAdminPage permission', () => {
    const userAccessOverrides = {
      viewAdminPage: true,
    }

    setup(defaultProps, userAccessOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canAccessMNIDedupe permission', () => {
    const userAccessOverrides = {
      canAccessMNIDedupe: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.mniDeduplication,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canAccessMasterChargeTable permission', () => {
    const userAccessOverrides = {
      canAccessMasterChargeTable: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableMasterChargeTool,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canEditMasterLocationTool permission', () => {
    const userAccessOverrides = {
      canEditMasterLocationTool: true,
    }
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableMasterLocationTool,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canAccessAuditLog permission', () => {
    const userAccessOverrides = {
      canAccessAuditLog: true,
    }

    setup(defaultProps, userAccessOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canAccessRecordsDataStoreModule permission', () => {
    const userAccessOverrides = {
      viewAdminPage: false,
      canAccessRecordsDataStoreModule: true,
    }

    setup(defaultProps, userAccessOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canAccessStandardsDataStoreModule permission', () => {
    const userAccessOverrides = {
      viewAdminPage: false,
      canAccessStandardsDataStoreModule: true,
    }

    setup(defaultProps, userAccessOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it('should display AdminConsole button with canConfigureDataStore permission', () => {
    const userAccessOverrides = {
      viewAdminPage: false,
      canConfigureDataStore: true,
    }

    setup(defaultProps, userAccessOverrides)
    screen.getByLabelText(/Administrator Console/i)
  })

  it.each([true, false])(
    'should display a confirmation on logout for isEdcaContext: [%s]',
    async (edcaContext) => {
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(edcaContext)
      setup(defaultProps)
      fireEvent.click(screen.getByRole('button', { name: /log out/i }))
      await screen.findByText('Are you sure you want to Logout?')
    }
  )

  it('should display cases button for records when cases are enabled', () => {
    const userAccessOverrides = {
      viewInvestigationsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableInvestigations,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/cases/i)
  })

  it('should not display cases button for standards when cases are enabled for records', () => {
    const userAccessOverrides = {
      viewInvestigationsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableInvestigations,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/cases/i)).toBeNull()
  })

  it('should display axon notes button if notes is enabled on the records side with no incident', () => {
    const userAccessOverrides = {
      viewInvestigationsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableInvestigations,
        isStandardsActive: false,
        getPartnerModules: () => [ModuleEnums.ENotes, ModuleEnums.Events],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.getByLabelText(/axon notes module/i)).toBeDefined()
  })

  it('should display axon records button if notes and incident modules are enabled', () => {
    const userAccessOverrides = {
      viewInvestigationsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableInvestigations,
        isStandardsActive: false,
        getPartnerModules: () => [ModuleEnums.ENotes, ModuleEnums.Incidents, ModuleEnums.Events],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.getByLabelText(/axon records/i)).toBeDefined()
  })

  it('should display cases button for standards when cases are enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableStandardsInvestigations,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/cases/i)
  })

  it('should not display cases button for standards when cases are not enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enableStandardsInvestigations,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/cases/i)).toBeNull()
  })

  it("should not display cases button for standards when user doesn't have appropriate permissions", () => {
    const userAccessOverrides = {
      viewInvestigationsModule: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableStandardsInvestigations,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/cases/i)).toBeNull()
  })

  it('should display ENotes button with enableEnotesModule permission', () => {
    const userAccessOverrides = {
      enableENotesModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableENotesModule,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Notes/i)
  })

  it('should not display cases button for records when cases are enabled for standards', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableStandardsInvestigations,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/cases/i)).toBeNull()
  })

  it('should not display property button if property inventory feature flag is disabled & canViewPepDashboard, canViewPepScanPage permissions are disabled', () => {
    const userAccessOverrides = {
      canViewPepDashboard: false,
      canViewPepScanPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enablePropertyInventory,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByText(/property/i)).toBeNull()
  })

  it('should not display property button if property inventory feature flag is enabled & canViewPepDashboard, canViewPepQualityControlSessionList, canViewPepScanPage permissions are disabled', () => {
    const userAccessOverrides = {
      canViewPepDashboard: false,
      canViewPepQualityControlSessionList: false,
      canViewPepScanPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enablePropertyInventory,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByText(/property/i)).toBeNull()
  })

  it('should not display property button if property inventory feature flag is disabled & canViewPepDashboard, canViewPepScanPage permissions are disabled && canViewPepQualityControlSessionList permission is enabled ', () => {
    const userAccessOverrides = {
      canViewPepDashboard: false,
      canViewPepQualityControlSessionList: true,
      canViewPepScanPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enablePropertyInventory,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByText(/property/i)).toBeNull()
  })

  it('should display property button if property inventory feature flag is enabled & canViewPepDashboard, canViewPepScanPage permissions are disabled && canViewPepQualityControlSessionList permission is enabled ', () => {
    const userAccessOverrides = {
      canViewPepDashboard: false,
      canViewPepQualityControlSessionList: true,
      canViewPepScanPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enablePropertyInventory ||
          attr === AgencyAttributesEnums.enablePep,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/property/i)
  })

  it('should display property button for records when pep is enabled', () => {
    const userAccessOverrides = {
      canViewPepDashboard: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => {
          return attr === AgencyAttributesEnums.enablePep
        },
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/property/i)
  })

  it('should only display PEP nav property button for records when enablePep is enabled', () => {
    const userAccessOverrides = { canViewPepDashboard: true, canViewPepScanPage: true }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => {
          return attr === AgencyAttributesEnums.enablePep
        },
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/property/i)
  })

  it('should display bookings icon button for records when booking feature flag is enabled and a permission is granted', () => {
    const userAccessOverrides = {
      canAccessBookingsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableBookings,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/bookings/i)
  })

  it('should not display bookings icon button when bookings feature flag is not enabled while permission is still granted', () => {
    const userAccessOverrides = {
      canAccessBookingsModule: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enableBookings,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/bookings/i)).toBeNull()
  })

  it('should not display bookings icon button when bookings feature flag is enabled while permission is not granted', () => {
    const userAccessOverrides = {
      canAccessBookingsModule: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableBookings,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/bookings/i)).toBeNull()
  })

  it('should display compliance icon for records when compliance feature flag is enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableComplianceModule,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/compliance/i)
  })

  it('should not display compliance icon for records when compliance feature flag is not enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enableComplianceModule,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/compliance/i)).toBeNull()
  })

  it('should not display property button when pep dashboard permission is not enabled', () => {
    const userAccessOverrides = {
      canViewPepDashboard: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enablePep,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/property/i)).toBeNull()
  })

  it('should not display property scan button when pep scan permission is not enabled', () => {
    const userAccessOverrides = {
      canViewPepScanPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enablePep,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/scan/i)).toBeNull()
  })

  it('should not display property and scan buttons in standards when pep is enabled for records', () => {
    const userAccessOverrides = {
      canViewPepDashboard: true,
      canViewPepScanPage: true,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enablePep,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    expect(screen.queryByLabelText(/property/i)).toBeNull()
    expect(screen.queryByLabelText(/scan/i)).toBeNull()
  })

  it('should not display eis button for standards when eis is not enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr !== AgencyAttributesEnums.enableEIS,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/early intervention/i)).toBeNull()
  })

  it('should not display eis button for standards when eis is enabled but user does not have access', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enableEIS,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/early intervention/i)).toBeNull()
  })

  it('should display eis button for standards when eis is enabled and user has access', () => {
    const userAccessOverrides = { canAccessEis: true }
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enableEIS,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/early intervention/i)
  })

  it('should not display eis button for records when eis is enabled for standards', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => attr === AgencyAttributesEnums.enableEIS,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/early intervention/i)).toBeNull()
  })

  it('should not display analytics button for standards when analytics is not enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr !== AgencyAttributesEnums.enableAnalytics,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/analytics/i)).toBeNull()
  })

  it('should display analytics button for standards when analytics is enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableAnalytics,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/analytics/i)
  })

  it('should display analytics button for records when analytics is enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableAnalytics,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/analytics/i)
  })

  it('should not display analytics button for records when analytics is not enabled', () => {
    const userContextOverrides = {
      overrides: {
        isStandardsActive: true,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    expect(screen.queryByLabelText(/analytics/i)).toBeNull()
  })

  it('should display help icon for both records and standards when page is landing', () => {
    const userContextOverrides = {
      overrides: {
        isStandardsActive: true,
      },
    }
    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/open myaxon help/i)
  })

  it('should display personnel button for both records and standards when personnel is enabled', () => {
    const userContextOverrides = {
      overrides: {
        isStandardsActive: true,
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enablePersonnel,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/personnel/i)
  })

  it('should display training button for both records and standards when training is enabled', () => {
    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) =>
          attr === AgencyAttributesEnums.enableTraining,
      },
    }

    setup(defaultProps, {}, userContextOverrides)
    screen.getByLabelText(/training/i)
  })

  // modules config cases
  it('should display only admin panel if no modules are set', () => {
    const userAccessOverrides = {
      viewAdminPage: true,
    }
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => [],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Administrator Console/i)
    expect(screen.queryByLabelText(/tasks/i)).toBeNull()
    expect(screen.queryByLabelText(/search/i)).toBeNull()
  })

  it('should display records tasks and search if INCIDENTS is in modules config', () => {
    const userAccessOverrides = {
      viewAdminPage: true,
    }
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => ['INCIDENTS'],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/Administrator Console/i)
    screen.getByLabelText(/tasks/i)
    screen.getByLabelText(/search/i)
  })

  it('should display notes and search if E_NOTES is in modules config', () => {
    const userAccessOverrides = {}
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => ['E_NOTES'],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText('Notes')
    screen.getByLabelText(/search/i)
  })

  it('should display records tasks, notes and search if INCIDENTS and E_NOTES are in modules config', () => {
    const userAccessOverrides = {}
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => ['INCIDENTS', 'E_NOTES'],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/tasks/i)
    screen.getByLabelText(/notes/i)
    screen.getByLabelText(/search/i)
  })

  it('should not display search multiple times', () => {
    const userAccessOverrides = {
      viewSearchPage: true,
    }
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => ['INCIDENTS', 'E_NOTES'],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    const search = screen.getAllByLabelText(/search/i)
    expect(search).toHaveLength(1)
  })

  it('should not display upper nav items when user does not have standards access', () => {
    const userAccessOverrides = {
      standardsAccess: false, // This will trigger the early return
      viewAdminPage: true,
      viewInvestigationsModule: true,
      viewStandardsSearchPage: true,
      canAccessEis: true,
      viewAnalyticsPage: true,

      // Disable records features to isolate the test
      viewRecordsTasks: false,
      canViewAllReports: false,
      viewSearchPage: false,
    }

    const userContextOverrides = {
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => {
          return (
            attr === AgencyAttributesEnums.enableStandardsInvestigations ||
            attr === AgencyAttributesEnums.enableEIS ||
            attr === AgencyAttributesEnums.enableAnalytics
          )
        },
        getPartnerModules: () => [],
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)

    // These standards-specific items should NOT be present due to early return
    expect(screen.queryByLabelText(/cases/i)).toBeNull() // standardsCaseTasks
    expect(screen.queryByLabelText(/search/i)).toBeNull() // schemaDrivenSearchStandards
    expect(screen.queryByLabelText(/early intervention/i)).toBeNull() // eisAlerts
    expect(screen.queryByLabelText(/analytics/i)).toBeNull() // analytics

    // But basic navigation items should still be present
    screen.getByLabelText(/Administrator Console/i)
    screen.getByLabelText(/switch to night mode/i)
    screen.getByLabelText(/open myaxon help/i)
    screen.getByLabelText(/log out/i)
  })

  it('should display "Axon Standards" banner when EWSACCESS is enabled and TREND_AGENCY is disabled', () => {
    const userAccessOverrides = {
      standardsAccess: true,
    }

    const userContextOverrides = {
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) => attr === PartnerFeatureEnums.EWSACCESS,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/axon standards/i)
  })

  it('should display "Axon Standards" banner when both EWSACCESS and TREND_AGENCY are enabled', () => {
    const userAccessOverrides = {
      standardsAccess: true,
    }

    const userContextOverrides = {
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) =>
          attr === PartnerFeatureEnums.EWSACCESS || attr === PartnerFeatureEnums.TREND_AGENCY,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/axon standards/i)
  })

  it('should handle search navigation with clearing intent', () => {
    setup(defaultProps)

    // Find and click a search button - this should trigger the navigation logic
    const searchButton = screen.getByLabelText(/search/i)
    expect(searchButton).toBeInTheDocument()

    // Clicking should not crash and should trigger the navigation code path
    expect(() => {
      fireEvent.click(searchButton)
    }).not.toThrow()
  })

  it('should handle normal navigation without clearing intent', () => {
    const userAccessOverrides = {
      viewRecordsTasks: true,
    }
    const userContextOverrides = {
      overrides: {
        getPartnerModules: () => [ModuleEnums.Incidents],
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)

    const tasksButton = screen.getByLabelText(/tasks/i)
    expect(tasksButton).toBeInTheDocument()

    expect(() => {
      fireEvent.click(tasksButton)
    }).not.toThrow()
  })

  it('should display "Axon Trend" banner when EWSACCESS is disabled and TREND_AGENCY is enabled', () => {
    const userAccessOverrides = {
      standardsAccess: true,
    }

    const userContextOverrides = {
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) => attr === PartnerFeatureEnums.TREND_AGENCY,
        isStandardsActive: true,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)
    screen.getByLabelText(/axon trend/i)
  })

  it('should have standardsItem when TREND_AGENCY is enabled and EWSACCESS is disabled', () => {
    const userAccessOverrides = {
      standardsAccess: true,
      canViewAllReports: true,
    }

    const userContextOverrides = {
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) =>
          attr === PartnerFeatureEnums.TREND_AGENCY || attr === PartnerFeatureEnums.RMSACCESS,
        isStandardsActive: false,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)

    // Click the app switcher to open dropdown and check available apps
    const appSwitcher = screen.getByRole('button', { name: /axon records/i })
    fireEvent.click(appSwitcher)

    screen.getByText(/axon trend/i)
  })

  it('should have standardsItem when both EWSACCESS and TREND_AGENCY are enabled', () => {
    const userAccessOverrides = {
      standardsAccess: true,
      canViewAllReports: true,
    }

    const userContextOverrides = {
      overrides: {
        hasPartnerFeature: (attr: PartnerFeatureEnums) =>
          attr === PartnerFeatureEnums.EWSACCESS ||
          attr === PartnerFeatureEnums.TREND_AGENCY ||
          attr === PartnerFeatureEnums.RMSACCESS,
        isStandardsActive: false,
      },
    }

    setup(defaultProps, userAccessOverrides, userContextOverrides)

    // Click the app switcher to open dropdown and check available apps
    const appSwitcher = screen.getByRole('button', { name: /axon records/i })
    fireEvent.click(appSwitcher)

    screen.getByText(/axon standards/i)
  })

  describe('In-App Notifications', () => {
    it('should display notifications button when enableInAppNotification attribute is enabled', () => {
      const userContextOverrides = {
        overrides: {
          hasAttribute: (attr: AgencyAttributesEnums) =>
            attr === AgencyAttributesEnums.enableInAppNotification,
        },
      }

      setup(defaultProps, {}, userContextOverrides)
      screen.getByLabelText(/notifications/i)
    })

    it('should not display notifications button when enableInAppNotification attribute is disabled', () => {
      const userContextOverrides = {
        overrides: {
          hasAttribute: (attr: AgencyAttributesEnums) =>
            attr !== AgencyAttributesEnums.enableInAppNotification,
        },
      }

      setup(defaultProps, {}, userContextOverrides)
      expect(screen.queryByLabelText(/notifications/i)).toBeNull()
    })

    it('should open notifications drawer when notifications button is clicked', () => {
      const userContextOverrides = {
        overrides: {
          hasAttribute: (attr: AgencyAttributesEnums) =>
            attr === AgencyAttributesEnums.enableInAppNotification,
        },
      }

      setup(defaultProps, {}, userContextOverrides)

      const notificationsButton = screen.getByLabelText(/notifications/i)
      expect(() => {
        fireEvent.click(notificationsButton)
      }).not.toThrow()
      expect(notificationsButton).toBeInTheDocument()
    })
  })

  describe('EDCA Context Behavior', () => {
    const testAgencyPartnerId = 'test-agency-123'

    beforeEach(() => {
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)
      jest.spyOn(hooks, 'useAgencyPartnerIdFromRoute').mockReturnValue(testAgencyPartnerId)
    })

    it('should not display other modules in EdcaContext', () => {
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)
      const userAccessOverrides = { viewAdminPage: true }
      const userContextOverrides = {
        overrides: {
          getPartnerModules: () => [],
        },
      }

      setup(defaultProps, userAccessOverrides, userContextOverrides)
      expect(screen.queryByLabelText(/tasks/i)).toBeNull()
      expect(screen.queryByLabelText(/search/i)).toBeNull()
      expect(screen.queryByLabelText(/property/i)).toBeNull()
      expect(screen.queryByLabelText(/scan/i)).toBeNull()
      expect(screen.queryByLabelText(/early intervention/i)).toBeNull()
      expect(screen.queryByLabelText(/bookings/i)).toBeNull()
      expect(screen.queryByLabelText(/cases/i)).toBeNull()
      expect(screen.queryByLabelText(/Notes/i)).toBeNull()
      expect(screen.queryByLabelText(/axon notes module/i)).toBeNull()
      expect(screen.queryByLabelText(/axon records/i)).toBeNull()

      // Should display exactly these 4 nav items in EDCA context
      screen.getByLabelText(/Administrator Console/i)
      screen.getByLabelText(/switch to night mode/i)
      screen.getByLabelText(/open myaxon help/i)
      screen.getByLabelText(/log out/i)
    })

    it('should not show AdminConsole when user has no admin permissions in EDCA context', () => {
      const userAccessOverrides = {
        viewAdminPage: false,
      }

      setup(defaultProps, userAccessOverrides)
      expect(screen.queryByLabelText(/Administrator Console/i)).toBeNull()
    })
  })
})
