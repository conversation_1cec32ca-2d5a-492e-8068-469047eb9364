/* eslint-disable complexity, max-statements, max-lines */
import * as React from 'react'
import { ConfirmDialog, GlobalNav } from 'iris-styleguide'
import { LogOutReason, logOutSessionApi } from 'shared/session'
import { PageModuleName, moduleName } from 'shared/pageSettings'
import { Route, useHistory, useLocation } from 'react-router-dom'
import { edcaRoutes, generateRoute, getRouteModule, routes } from 'shared/routes'
import { permissionKeys, useUserContext } from 'store/context/user'

import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { ButtonVariant } from '@axon-enterprise/spark'
import { ModuleEnum } from 'static/graphql/types'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import { asTypedObject } from 'shared/types/utilTypes'
import { flatMap, isEmpty, uniqBy } from 'lodash'
import { log } from 'shared/log'
import { isEdcaContext } from 'shared/envUtils'
import { openInNewTab } from 'shared/utils'
import { useAgencyPartnerIdFromRoute, useUserAccess } from 'shared/hooks'
import { ModuleEnums } from 'shared/modules'
import styles from './styles.css'
import {
  AppType,
  GetAppItemsArgs,
  GetItemsArgs,
  GetRecordsItemsArgs,
  GetStandardsItemsArgs,
  GlobalNavItem,
  HelpKey,
  LogoutKey,
  NotificationsKey,
  SwitcherAppItem,
  ThemeKey,
} from './types'
import { NotificationsDrawer } from '../notificationDrawer'

const LOGOUT_KEY: LogoutKey = 'logout'
const THEME_KEY: ThemeKey = 'toggle-theme'
const HELP_KEY: HelpKey = 'help-icon'
const NOTIFICATIONS_KEY: NotificationsKey = 'notifications'

const urls = {
  reportWriter: routes.TASKS.route({}),
  schemaDrivenSearch: routes.SCHEMA_DRIVEN_SEARCH.route({}),
  schemaDrivenSearchStandards: routes.SCHEMA_DRIVEN_SEARCH_STANDARDS.route({}),
  search: routes.SEARCH.route({}),
  standardsTasks: routes.STANDARDS_TASKS.route({}),
  standardsSearch: routes.STANDARDS_SEARCH.route({}),
  caseTasks: routes.CASE_TASKS.route({}),
  analytics: routes.ANALYTICS.route({}),
  evidence: routes.EVIDENCE.route(),
  ncicSearch: routes.NCIC_SEARCH.route(),
  propertyManagement: routes.PROPERTY_MANAGEMENT.route({}),
  standardsCaseTasks: routes.STANDARDS_CASE_TASKS.route({}),
  bookings: routes.BOOKINGS.route({}),
  compliance: routes.COMPLIANCE.route({}),
  eNotes: routes.ENOTES.route(),
  eis: routes.EIS.route({}),
  warrants: routes.WARRANTS.route({}),
  personnel: routes.PERSONNEL.route(),
  training: routes.TRAINING.route({}),
}

const appItems = () =>
  asTypedObject<SwitcherAppItem>()({
    records: {
      label: __('Axon Records'),
      value: AppType.Records,
      to: urls.reportWriter,
    },
    notes: {
      label: __('Axon Notes Module'),
      value: AppType.Records,
      to: urls.eNotes,
    },
    standards: {
      label: __('Axon Standards'),
      value: AppType.Standards,
      to: urls.standardsTasks,
    },
    evidence: {
      label: __('Axon Evidence'),
      value: AppType.Evidence,
      to: urls.evidence,
    },
    ncicSearch: {
      label: __('State/Federal Search'),
      value: AppType.NcicSearch,
      to: urls.ncicSearch,
    },
  })

const navItems = () =>
  asTypedObject<GlobalNavItem>()({
    bookings: {
      glyph: 'Booking',
      label: __('Bookings'),
      value: moduleName.bookings,
    },
    compliance: {
      glyph: 'AssignmentTurnedIn', // TODO: Replace with correct compliance icon - RMS-86625
      label: __('Compliance'),
      value: moduleName.compliance,
    },
    eNotes: {
      glyph: 'LogoNotes',
      label: __('Notes'),
      value: moduleName.eNotes,
    },
    warrants: {
      glyph: 'Warrants',
      label: __('Warrants'),
      value: moduleName.warrants,
    },
    reportWriter: {
      glyph: 'SendForward',
      label: __('Reports'),
      value: moduleName.reportWriter,
    },
    standardsTasks: {
      glyph: 'Edit',
      label: __('Tasks'),
      value: moduleName.standardsTasks,
    },
    caseTasks: {
      glyph: 'LogoCases',
      label: __('Cases'),
      value: moduleName.caseTasks,
    },
    schemaDrivenSearch: {
      glyph: 'Search',
      label: __('Search'),
      value: moduleName.schemaDrivenSearch,
    },
    search: {
      glyph: 'Search',
      label: __('Search'),
      value: moduleName.search,
    },
    schemaDrivenSearchStandards: {
      glyph: 'Search',
      label: __('Search'),
      value: moduleName.schemaDrivenSearchStandards,
    },
    standardsSearch: {
      glyph: 'Search',
      label: __('Search'),
      value: moduleName.standardsSearch,
    },
    analytics: {
      glyph: 'Metrics',
      label: __('Analytics'),
      value: moduleName.analytics,
    },
    themeLight: {
      glyph: 'ThemeDark',
      label: __('Switch to Night Mode'),
      value: THEME_KEY,
    },
    themeDark: {
      glyph: 'Light',
      label: __('Switch to Light Mode'),
      value: THEME_KEY,
    },
    notifications: {
      glyph: 'Notifications',
      label: __('Notifications'),
      value: NOTIFICATIONS_KEY,
    },
    adminConsole: {
      glyph: 'Settings',
      label: __('Administrator Console'),
      value: moduleName.adminConsole,
    },
    logout: {
      glyph: 'Logout',
      label: __('Log out'),
      iconSize: 'M',
      value: LOGOUT_KEY,
    },
    scan: {
      glyph: 'BarcodeScan',
      label: __('Scan'),
      value: moduleName.scan,
    },
    propertyMyTasks: {
      glyph: 'AssignmentTurnedIn',
      label: __('Property'),
      value: moduleName.propertyMyTasks,
    },
    propertyManagement: {
      glyph: 'AssignmentTurnedIn',
      label: __('Property'),
      value: moduleName.propertyManagement,
    },
    standardsCaseTasks: {
      glyph: 'LogoCases',
      label: __('Cases'),
      value: moduleName.standardsCaseTasks,
    },
    eisAlerts: {
      glyph: 'EIS',
      label: __('Early Intervention'),
      value: moduleName.earlyIntervention,
    },
    help: {
      glyph: 'Help',
      label: __('Open MyAxon Help'),
      value: HELP_KEY,
    },
    personnel: {
      glyph: 'Person',
      label: __('Personnel'),
      value: moduleName.personnel,
    },
    training: {
      glyph: 'School',
      label: __('Training'),
      value: moduleName.training,
    },
  })

function getAppItems({
  hasAttribute,
  hasPartnerFeature,
  toggleActiveModule,
  modules,
  hasRecordsAccess,
  hasStandardsAccess,
  isStandardsActive,
}: GetAppItemsArgs) {
  const agencyHasRecords = hasPartnerFeature(PartnerFeatureEnums.RMSACCESS)
  const agencyHasStandards = hasPartnerFeature(PartnerFeatureEnums.EWSACCESS)
  const agencyHasTrend = hasPartnerFeature(PartnerFeatureEnums.TREND_AGENCY)
  const isNcicNavEnabled = hasAttribute(AgencyAttributesEnums.enableNavToNcicSearch)

  const agencyHasStandardsOrTrend = agencyHasStandards || agencyHasTrend
  const recordsItem = appItems().records
  const notesItem = appItems().notes
  recordsItem.onClick = toggleActiveModule
  const standardsItem = {
    ...appItems().standards,
    label: agencyHasTrend && !agencyHasStandards ? __('Axon Trend') : __('Axon Standards'),
    onClick: toggleActiveModule,
  }

  const items: SwitcherAppItem[] = []
  if (isEdcaContext()) {
    return items
  }

  if ((agencyHasRecords && hasRecordsAccess) || !isStandardsActive) {
    if (modules?.includes(ModuleEnums.ENotes) && !modules?.includes(ModuleEnums.Incidents)) {
      items.push(notesItem)
    } else {
      items.push(recordsItem)
    }
  }
  if ((agencyHasStandardsOrTrend && hasStandardsAccess) || isStandardsActive) {
    items.push(standardsItem)
  }

  if (!hasRecordsAccess && !hasStandardsAccess) {
    // if user has no configured permissions, add agency's default module
    items.push(agencyHasRecords ? recordsItem : standardsItem)
  }

  items.push(appItems().evidence)
  if (isNcicNavEnabled) {
    items.push(appItems().ncicSearch)
  }
  return items
}

function getActiveApp(availableApps: SwitcherAppItem[], isStandardsActive: boolean) {
  const flattenedApps = flatMap(availableApps)

  return isStandardsActive
    ? flattenedApps.find((item) => item.value === AppType.Standards)
    : flattenedApps.find((item) => item.value === AppType.Records)
}

function getRecordsItems({ access, hasAttribute, modules }: GetRecordsItemsArgs) {
  const {
    viewSearchPage,
    viewInvestigationsModule,
    viewAnalyticsPage,
    canAccessBookingsModule,
    canViewPepDashboard,
    canViewPepQualityControlSessionList,
    canViewPepScanPage,
    warrantModuleView,
    canViewAllReports,
  } = access

  const hasAnalytics = hasAttribute(AgencyAttributesEnums.enableAnalytics)
  const hasInvestigations = hasAttribute(AgencyAttributesEnums.enableInvestigations)
  const isPepEnabled = hasAttribute(AgencyAttributesEnums.enablePep)
  const isBookingsEnabled = hasAttribute(AgencyAttributesEnums.enableBookings)
  const isENotesEnabled = hasAttribute(AgencyAttributesEnums.enableENotesModule)
  const isWarrantsEnabled = hasAttribute(AgencyAttributesEnums.enableWarrantsModule)
  const isPropertyInventoryEnabled = hasAttribute(AgencyAttributesEnums.enablePropertyInventory)
  const isComplianceEnabled = hasAttribute(AgencyAttributesEnums.enableComplianceModule)

  const showPropertyManagementPage =
    (isPropertyInventoryEnabled && canViewPepQualityControlSessionList) ||
    canViewPepDashboard ||
    canViewPepScanPage

  // check modules here if modules is a list value and hide tasks if not set
  if (modules && isEmpty(modules)) {
    return []
  }

  const upperItems: GlobalNavItem[] = []

  const getSearchItem = (items: GlobalNavItem[]) => {
    if (viewSearchPage) {
      items.push(navItems().schemaDrivenSearch)
    }
  }

  if (!modules || modules.includes(ModuleEnums.Incidents)) {
    const updatedTaskNavItem = { ...navItems().reportWriter, label: __('Tasks') }
    upperItems.push(updatedTaskNavItem)

    if (!canViewAllReports) {
      return upperItems
    }

    if (hasInvestigations && viewInvestigationsModule) {
      upperItems.push(navItems().caseTasks)
    }

    getSearchItem(upperItems)

    if (isPepEnabled && showPropertyManagementPage) {
      upperItems.push(navItems().propertyManagement)
    }

    if (hasAnalytics && viewAnalyticsPage) {
      upperItems.push(navItems().analytics)
    }

    if (isBookingsEnabled && canAccessBookingsModule) {
      upperItems.push(navItems().bookings)
    }

    if (isWarrantsEnabled && warrantModuleView) {
      upperItems.push(navItems().warrants)
    }

    if (isComplianceEnabled) {
      upperItems.push(navItems().compliance)
    }
  }
  if (modules?.includes(ModuleEnums.ENotes) || isENotesEnabled) {
    upperItems.push(navItems().eNotes)
    getSearchItem(upperItems)
  }

  return uniqBy(upperItems, (item: GlobalNavItem) => item.value)
}

function getStandardsItems({ access, hasAttribute, modules }: GetStandardsItemsArgs) {
  if (modules && isEmpty(modules)) {
    return []
  }

  const {
    canAccessEis,
    viewAnalyticsPage,
    viewStandardsSearchPage,
    standardsAccess,
    viewInvestigationsModule,
  } = access
  const hasInvestigations = hasAttribute(AgencyAttributesEnums.enableStandardsInvestigations)
  const hasAnalytics = hasAttribute(AgencyAttributesEnums.enableAnalytics)

  const upperItems = [navItems().standardsTasks]

  if (!standardsAccess) {
    return upperItems
  }

  if (hasInvestigations && viewInvestigationsModule) {
    upperItems.push(navItems().standardsCaseTasks)
  }

  if (viewStandardsSearchPage) {
    upperItems.push(navItems().schemaDrivenSearchStandards)
  }
  if (hasAttribute(AgencyAttributesEnums.enableEIS) && canAccessEis) {
    upperItems.push(navItems().eisAlerts)
  }
  if (hasAnalytics && viewAnalyticsPage) {
    upperItems.push(navItems().analytics)
  }

  return upperItems
}

function getItems({ access, permissions, hasAttribute, modules, isStandardsActive }: GetItemsArgs) {
  const { enableDarkMode } = permissions
  const {
    canAccessAuditLog,
    canAccessMasterChargeTable,
    canAccessMNIDedupe,
    canEditMasterLocationTool,
    viewAdminPage,
    canAccessRecordsDataStoreModule,
    canAccessStandardsDataStoreModule,
    canConfigureDataStore,
  } = access

  const upperItems = isStandardsActive
    ? getStandardsItems({ access, hasAttribute, modules })
    : getRecordsItems({ access, hasAttribute, modules })

  const lowerItems: GlobalNavItem[] = []
  const showMasterChargeTool =
    hasAttribute(AgencyAttributesEnums.enableMasterChargeTool) && canAccessMasterChargeTable
  const showMasterLocationTool =
    hasAttribute(AgencyAttributesEnums.enableMasterLocationTool) && canEditMasterLocationTool
  const showMNITool = hasAttribute(AgencyAttributesEnums.mniDeduplication) && canAccessMNIDedupe
  const showPersonnel = hasAttribute(AgencyAttributesEnums.enablePersonnel)
  const showTraining = hasAttribute(AgencyAttributesEnums.enableTraining)

  if (showPersonnel) {
    upperItems.push(navItems().personnel)
  }
  if (showTraining) {
    upperItems.push(navItems().training)
  }

  const canAccessDataStoreModule =
    canAccessRecordsDataStoreModule || canAccessStandardsDataStoreModule || canConfigureDataStore

  const canViewAdminConsolePermissions = [
    viewAdminPage,
    showMNITool,
    showMasterChargeTool,
    showMasterLocationTool,
    canAccessAuditLog,
    canAccessDataStoreModule,
  ]

  if (hasAttribute(AgencyAttributesEnums.enableInAppNotification)) {
    lowerItems.push(navItems().notifications)
  }

  if (canViewAdminConsolePermissions.some((item) => !!item)) {
    lowerItems.push(navItems().adminConsole)
  }

  const themeIcon = enableDarkMode ? navItems().themeDark : navItems().themeLight
  lowerItems.push(themeIcon)

  lowerItems.push(navItems().help)

  lowerItems.push(GlobalNav.separator)
  lowerItems.push(navItems().logout)

  return {
    upper: isEdcaContext() ? [] : upperItems,
    lower: lowerItems,
  }
}

type Props = {
  activeModule: PageModuleName
}

export const Nav = ({ activeModule }: Props) => {
  const {
    user,
    permissions,
    toggleSetting,
    toggleActiveModule,
    hasAttribute,
    hasPartnerFeature,
    isStandardsActive,
    getPartnerModules,
  } = useUserContext()
  const modules: ModuleEnums[] | undefined = getPartnerModules()
  const [isNotificationsDrawerOpen, setIsNotificationsDrawerOpen] = React.useState(false)

  const { access, loading } = useUserAccess([
    'canAccessAuditLog',
    'canAccessEis',
    'canAccessMasterChargeTable',
    'canEditMasterLocationTool',
    'canAccessMNIDedupe',
    'canAccessBookingsModule',
    'canViewAllReports',
    'canViewPepDashboard',
    'canViewPepScanPage',
    'canViewPepQualityControlSessionList',
    'standardsAccess',
    'viewAdminPage',
    'viewAnalyticsPage',
    'viewInvestigationsModule',
    'viewSearchPage',
    'viewStandardsSearchPage',
    'warrantModuleView',
    'canAccessRecordsDataStoreModule',
    'canAccessStandardsDataStoreModule',
    'canConfigureDataStore',
  ])
  const history = useHistory()
  const agencyPartnerId = useAgencyPartnerIdFromRoute()

  const adminConsoleUrl = generateRoute({
    edcaRoute: edcaRoutes.ADMIN.route,
    baseRoute: routes.ADMIN.route,
    routeParams: {},
    agencyPartnerId,
  })

  const { standardsAccess: hasStandardsAccess, canViewAllReports: hasRecordsAccess } = access

  const { pathname } = useLocation()
  React.useEffect(() => {
    const hasRecords = hasPartnerFeature(PartnerFeatureEnums.RMSACCESS)
    const hasStandards = hasPartnerFeature(PartnerFeatureEnums.EWSACCESS)
    const hasTrend = hasPartnerFeature(PartnerFeatureEnums.TREND_AGENCY)
    const canSwitchBetweenModules = hasRecords && (hasStandards || hasTrend)

    if (canSwitchBetweenModules) {
      const currentRouteModule = getRouteModule(pathname)
      if (currentRouteModule === ModuleEnum.Records && isStandardsActive) {
        toggleActiveModule()
      } else if (currentRouteModule === ModuleEnum.Standards && !isStandardsActive) {
        toggleActiveModule()
      }
    }
  })

  if (loading) {
    return <GlobalNav className={styles.nav} />
  }

  const items = getItems({
    access,
    permissions,
    modules,
    hasAttribute,
    isStandardsActive,
  })
  const activeItem = flatMap(items).find(
    (item) => typeof item !== 'string' && item.value === activeModule
  )
  const availableApps = getAppItems({
    hasAttribute,
    hasPartnerFeature,
    toggleActiveModule,
    hasRecordsAccess,
    modules,
    hasStandardsAccess,
    isStandardsActive,
  })

  const activeAppItem = getActiveApp(availableApps, isStandardsActive)
  return (
    <Route>
      <ConfirmDialog
        title={__('Logout')}
        content={__('Are you sure you want to Logout?')}
        confirmVariant={ButtonVariant.critical}
        confirmText={__('Logout')}
        onClose={(confirmed) => {
          if (confirmed) {
            log.track('manual-user-logout')
            logOutSessionApi(LogOutReason.manual)
          }
        }}
        renderControl={({ openDialog }) =>
          !loading && (
            <GlobalNav
              className={styles.nav}
              items={items}
              appItems={availableApps}
              activeApp={activeAppItem}
              activeItem={typeof activeItem !== 'string' ? activeItem : undefined}
              avatar={{
                firstName: user.firstName,
                lastName: user.lastName,
              }}
              onChange={(value: string) => {
                if (value === LOGOUT_KEY) {
                  openDialog()
                } else if (value === THEME_KEY) {
                  log.track('change-theme', { darkMode: !permissions.enableDarkMode })
                  toggleSetting(permissionKeys.enableDarkMode)
                } else if (value === HELP_KEY) {
                  openInNewTab('https://my.axon.com/s/records-and-standards', 'Open myAxon Help')
                } else if (value === NOTIFICATIONS_KEY) {
                  log.track('open-notification-drawer')
                  setIsNotificationsDrawerOpen(true)
                } else if (value === moduleName.adminConsole) {
                  history.push(adminConsoleUrl)
                } else {
                  // Check if this is a search navigation that should trigger clearing
                  const searchModules: string[] = [
                    moduleName.schemaDrivenSearch,
                    moduleName.schemaDrivenSearchStandards,
                    moduleName.search,
                    moduleName.standardsSearch,
                  ]
                  const isSearchNavigation = searchModules.includes(value)

                  if (isSearchNavigation) {
                    // Navigate to search with clearing intent
                    history.push(urls[value], { clearingIntent: 'true' })
                  } else {
                    // Normal navigation
                    history.push(urls[value])
                  }
                }
              }}
            />
          )
        }
      />
      <NotificationsDrawer
        isOpen={isNotificationsDrawerOpen}
        setIsOpen={setIsNotificationsDrawerOpen}
      />
    </Route>
  )
}
