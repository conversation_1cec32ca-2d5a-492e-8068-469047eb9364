import * as React from 'react'
import {
  TabBar,
  TabBarProps,
  TabPanel,
  Tabs,
  TabsProps,
  TabsProvider,
} from '@axon-enterprise/spark'
import { Grid, GridProps, getSpacingProps } from '@axon-enterprise/electra'

type NextTabsProps = TabsProps & GridProps

export const NextTabPanel = TabPanel
export const NextTabsProvider = TabsProvider

export function NextTabs(props: NextTabsProps) {
  const [gridProps, tabsProps] = getSpacingProps(props)

  return (
    <Grid {...gridProps}>
      <div style={{ width: '100%' }}>
        <Tabs {...tabsProps} />
      </div>
    </Grid>
  )
}

NextTabs.TabItem = Tabs.TabItem

type NextTabBarProps = TabBarProps & GridProps

export function NextTabBar(props: NextTabBarProps) {
  const [gridProps, tabBarProps] = getSpacingProps(props)

  return (
    <Grid {...gridProps}>
      <div style={{ width: '100%' }}>
        <TabBar {...tabBarProps} />
      </div>
    </Grid>
  )
}

NextTabBar.Item = TabBar.Item
