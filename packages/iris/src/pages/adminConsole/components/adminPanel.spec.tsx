import React from 'react'
import * as envUtils from 'shared/envUtils'
import * as hooks from 'shared/hooks'
import * as userContext from 'store/context/user/userContext'
import * as formBuilderQueries from 'pages/adminConsole/components/forms/components/formBuilder/queries'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { MockApollo } from 'test/mocks/mockApollo'
import { createMemoryHistory, render, screen, waitFor } from 'test/utils'
import userEvent from '@testing-library/user-event'
import { FormBuilderAccessType, edcaRoutes, routes } from 'shared/routes'
import { mockUseUserContext } from 'test/mocks/userContext'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import * as useQueryAllInboxes from 'shared/queries/inboxesV2/allInboxes'
import * as useQueryCompactWorkflows from 'shared/queries/workflows/workflows'
import { AdminPanel } from './adminPanel'
import { testConstants } from './inboxManager/testConstants'

describe('adminPanel', () => {
  let defaultProps
  beforeEach(() => {
    sessionStorage.clear()
    defaultProps = {
      agencyDistricts: {},
      currentUser: {},
      userAccess: {
        canAccessAuditLog: true,
        canAccessMasterChargeTable: true,
        canAccessMNIDedupe: true,
        canEditMasterLocationTool: true,
        viewAdminPage: true,
        viewExpungements: true,
        viewInternalPrivileges: true,
      },
    }
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)
    jest.spyOn(useQueryAllInboxes, 'useQueryAllInboxes').mockImplementation(() => ({
      inboxesV2: [],
      loading: false,
      totalResults: 0,
      error: undefined,
      refetch: jest.fn(),
    }))

    jest.spyOn(useQueryCompactWorkflows, 'useQueryCompactWorkflows').mockImplementation(() => ({
      workflows: testConstants.workflows,
      workflowIds: testConstants.workflows.map((workflow) => workflow.id || ''),
      workflowsToFastForwardMap: new Map(),
      loading: false,
      totalResults: testConstants.workflows.length,
      error: undefined,
    }))

    jest.spyOn(formBuilderQueries, 'useGetFormWorkflowMappingQuery').mockImplementation(() => ({
      formWorkflowMapping: testConstants.formWorkflowMapping,
      loading: false,
      refetch: jest.fn(),
    }))
  })

  const renderWithRouter = (ui, { route = routes.ADMIN.route({ type: 'analytics' }) } = {}) => {
    const history = createMemoryHistory()
    history.push(route)

    return render(ui, { history })
  }

  describe('routes', () => {
    it('should render the User Management page', () => {
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'user-list' }) }
      )
      screen.getByText(/Users/<USER>
    })

    it('should render the Team List page', () => {
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'team-list' }) }
      )
      screen.getByText(/Search by team name/i)
    })

    it('should render the Announcement Banner page', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attr) => attr === AgencyAttributesEnums.enableAnnouncementBannerSelfUpdate,
        },
      })
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'announcement-banner-update' }) }
      )
      screen.getByText(/New Banner Action Button Text/)
    })

    it('should render the Classifications Tool page', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attr) => attr === AgencyAttributesEnums.enableClassificationsTool,
        },
      })

      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'classifications-tool' }) }
      )
      screen.getByText(/Restriction Classifications/i)
    })

    it('should render the incident merge tool page', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attr) => attr === AgencyAttributesEnums.enableIncidentMerge,
        },
      })
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'incident-merge-tool' }) }
      )
      screen.getByText(/Incident Merge Tool/i)
    })

    it('should render the property management page', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attr) => attr === AgencyAttributesEnums.enablePep,
        },
      })
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'property-management' }) }
      )
      screen.getByText(/Property Management/i)
    })

    describe('enableInboxManager', () => {
      it('should not have the inbox manager admin page if enableInboxManager is enabled and RMSAccess flag is not enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature !== PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableTaskInboxManager,
          },
        })

        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'inbox-manager' }) }
        )
        expect(screen.queryAllByText(/Inbox Manager/i)).toHaveLength(0)
      })

      it('should not have inbox manager page if enableInboxManager is disabled and RMSAccess flag is enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature === PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute !== AgencyAttributesEnums.enableTaskInboxManager,
          },
        })
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'inbox-manager' }) }
        )
        expect(screen.queryAllByText(/Inbox Manager/i)).toHaveLength(0)
      })

      it('should have inbox manager page if enableInboxManager is enabled and RMSAccess flag is enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature === PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableTaskInboxManager,
          },
        })
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'inbox-manager' }) }
        )

        userEvent.click(screen.getByText(/Acknowledge/i))
        expect(screen.queryAllByText(/Inbox Manager/i)).toHaveLength(2)
      })

      it('should not open the create inbox page if enableInboxManager is enabled and RMSAccess flag is not enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature !== PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableTaskInboxManager,
          },
        })

        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN_CREATE_INBOX.path }
        )
        expect(screen.queryByRole('heading', { name: /Create Inbox/i })).not.toBeInTheDocument()
      })

      it('should not open the create inbox page if enableInboxManager is disabled and RMSAccess flag is enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature === PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute !== AgencyAttributesEnums.enableTaskInboxManager,
          },
        })
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN_CREATE_INBOX.path }
        )
        expect(screen.queryByRole('heading', { name: /Create Inbox/i })).not.toBeInTheDocument()
      })

      it('should have the create inbox page if enableInboxManager is enabled and RMSAccess flag is enabled', () => {
        mockUseUserContext({
          overrides: {
            hasPartnerFeature: (partnerFeature: PartnerFeatureEnums) =>
              partnerFeature === PartnerFeatureEnums.RMSACCESS,
            hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableTaskInboxManager,
          },
        })
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN_CREATE_INBOX.path }
        )
        screen.getByRole('heading', { name: /Create Inbox/i })
      })
    })

    describe('enableAnalytics', () => {
      it('should have the analytics admin page if enableAnalyticsAdminPage is enabled', () => {
        jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
          ...userContext.contextDefaults,
          hasAttribute: (attribute) =>
            attribute === AgencyAttributesEnums.enableAnalytics ||
            attribute === AgencyAttributesEnums.enableAnalyticsAdminPage,
        }))
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>
        )
        const analyticsPage = screen.queryAllByText('Analytics')

        expect(analyticsPage).toHaveLength(1)
      })

      it('should not have the analytics admin page if enableAnalyticsAdminPage is not enabled', () => {
        jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
          ...userContext.contextDefaults,
          hasAttribute: () => false,
        }))

        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>
        )
        const analyticsPage = screen.queryAllByText('Analytics')

        expect(analyticsPage).toHaveLength(0)
      })

      it('should have the master location tool admin page if enableMasterLocationTool is enabled', async () => {
        jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
          ...userContext.contextDefaults,
          hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableMasterLocationTool,
        }))
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'master-location-tool' }) }
        )
        userEvent.click(screen.getByText('Acknowledge'))

        await screen.findByText('Master Location Tool')
      })
    })

    it('should render the Master Charge Tool page', () => {
      mockUseUserContext({
        overrides: {
          isLiteAgency: false,
          hasAttribute: (attr) => attr === AgencyAttributesEnums.enableMasterChargeTool,
        },
      })
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'master-charge-tool' }) }
      )
      screen.getByText(/Master Charge Tool/)
    })

    it('should have supervisor notes page [RMS-T13479]', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attr: AgencyAttributesEnums) =>
            attr === AgencyAttributesEnums.enableSupervisorNotes,
        },
      })
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN.route({ type: 'supervisor-notes' }) }
      )
      screen.getByText(/Feedback Note/)
    })

    it('should have the LFB admin page if enableFormBuilder is enabled', async () => {
      jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
        ...userContext.contextDefaults,
        hasAttribute: (attribute) => attribute === AgencyAttributesEnums.enableFormBuilder,
      }))
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN_FORMS.path({ formBuilderAccessType: FormBuilderAccessType.LFB }) }
      )
      userEvent.click(screen.getByText('Acknowledge'))

      await screen.findByText('Form Builder')
    })

    it('should not have the LFB admin page if enableFormBuilder is disabled and user is disallowed', async () => {
      jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
        ...userContext.contextDefaults,
        hasAttribute: () => false,
      }))
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        { route: routes.ADMIN_FORMS.path({ formBuilderAccessType: FormBuilderAccessType.LFB }) }
      )

      const analyticsPage = screen.queryAllByText('Form Builder')

      expect(analyticsPage).toHaveLength(0)
    })

    it('should have the EFB admin page if isEdcaContext is true', async () => {
      jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
        ...userContext.contextDefaults,
        hasAttribute: () => false,
        hasPartnerFeature: () => true,
      }))
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)
      jest.spyOn(hooks, 'useAgencyPartnerIdFromRoute').mockReturnValue('test-agency-123')

      // Mock this hook directly to avoid GraphQL schema issues
      jest.spyOn(formBuilderQueries, 'useGetFormWorkflowMappingQuery').mockReturnValue({
        formWorkflowMapping: {
          __typename: 'AgencyConfigWorkflow',
          defaultWorkflow: undefined,
          customReportWorkflows: [],
        },
        loading: false,
        refetch: jest.fn(),
      })

      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        {
          route: edcaRoutes.ADMIN_FORMS.route({
            formBuilderAccessType: FormBuilderAccessType.EFB,
            agencyPartnerId: 'test-agency-123',
          }),
        }
      )

      userEvent.click(screen.getByText('Acknowledge'))
      await screen.findByText('Form Builder')
    })

    it('should not have the EFB admin page if isEdcaContext is false', async () => {
      jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
        ...userContext.contextDefaults,
        hasAttribute: () => false,
        hasPartnerFeature: () => true,
      }))
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)
      renderWithRouter(
        <MockApollo>
          <AdminPanel {...defaultProps} />
        </MockApollo>,
        {
          route: edcaRoutes.ADMIN_FORMS.route({
            formBuilderAccessType: FormBuilderAccessType.EFB,
            agencyPartnerId: 'test-agency-123',
          }),
        }
      )

      const adminPage = screen.queryAllByText('Form Builder')
      expect(adminPage).toHaveLength(0)
    })

    it('should default to the mni-merge-tool page if all other cases are false', async () => {
      jest.spyOn(userContext, 'useUserContext').mockImplementation(() => ({
        ...userContext.contextDefaults,
        hasAttribute: (attribute) => attribute === AgencyAttributesEnums.mniDeduplication,
        hasPartnerFeature: () => false,
      }))
      jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)

      // Override all access properties to false except canAccessMNIDedupe to reach the default case
      const propsWithNoAccess = {
        ...defaultProps,
        userAccess: {
          viewAdminPage: false,
          canAccessAuditLog: false,
          canAccessMasterChargeTable: false,
          canEditMasterLocationTool: false,
          canAccessRecordsDataStoreModule: false,
          canAccessStandardsDataStoreModule: false,
          canAccessMNIDedupe: true, // Need this + mniDeduplication attribute for showMergeTool to be true
        },
      }

      renderWithRouter(
        <MockApollo>
          <AdminPanel {...propsWithNoAccess} />
        </MockApollo>,
        {
          route: routes.ADMIN.path,
        }
      )

      await waitFor(() => {
        screen.getByText(/MNI Merge Tool/)
      })
    })

    describe('DataStoreSecretGenerationPage', () => {
      it('should access to DataStore Secret Generation Page even cannot view AdminPage when give access to Records DataStore', async () => {
        const props: any = {
          agencyDistricts: {},
          currentUser: {},
          userAccess: {
            canAccessRecordsDataStoreModule: true,
          },
        }
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...props} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'datastore-secret-generation' }) }
        )

        screen.getByText(/DataStore Secret Generation/)
      })

      it('should access to DataStore Secret Generation Page even cannot view AdminPage when give access to Standards DataStore', async () => {
        const props: any = {
          agencyDistricts: {},
          currentUser: {},
          userAccess: {
            canAccessStandardsDataStoreModule: true,
          },
        }
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...props} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'datastore-secret-generation' }) }
        )

        screen.getByText(/DataStore Secret Generation/)
      })

      it('should not access to DataStore Secret Generation Page when give no access', async () => {
        render(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>
        )
        const adminPage = screen.queryAllByText(/DataStore Secret Generation/)
        expect(adminPage).toHaveLength(0)
      })
    })

    describe('DataStoreSettingsPage', () => {
      it('should access to DataStore Settings Page even cannot view AdminPage when have access canConfigureDataStore', async () => {
        const props: any = {
          agencyDistricts: {},
          currentUser: {},
          userAccess: {
            canConfigureDataStore: true,
          },
        }
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...props} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'datastore-settings' }) }
        )

        screen.getByText(/DataStore Settings/)
      })

      it('should not access to DataStore Settings Page when have no access', async () => {
        render(
          <MockApollo>
            <AdminPanel {...defaultProps} />
          </MockApollo>
        )
        const adminPage = screen.queryByText(/DataStore Settings/)
        expect(adminPage).toBeNull()
      })

      it('should direct to DataSTore Settings Page first when users have both canConfigureDataStore and canAccessDataStore', async () => {
        const props: any = {
          agencyDistricts: {},
          currentUser: {},
          userAccess: {
            canConfigureDataStore: true,
            canAccessRecordsDataStoreModule: true,
          },
        }
        renderWithRouter(
          <MockApollo>
            <AdminPanel {...props} />
          </MockApollo>,
          { route: routes.ADMIN.route({ type: 'datastore-settings' }) }
        )

        screen.getByText(/DataStore Settings/)
        const dataStoreSecretGenerationPage = screen.queryByText(/DataStore Secret Generation/)
        expect(dataStoreSecretGenerationPage).toBeNull()
      })
    })
  })
})
