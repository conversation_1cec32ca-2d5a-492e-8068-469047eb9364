/* eslint-disable max-lines */
import { useGrantLegacyFormBuilderAccess } from 'pages/adminConsole/pageSettings'
import { AlertPolicies } from 'pages/adminConsole/components/alertPolicies'
import { ClassificationsTool } from 'pages/adminConsole/components/classificationsTool'
import { ClassificationToolExceptions } from 'pages/adminConsole/components/classificationsToolExceptions/classificationsToolExceptions'
import { DataStoreSettings } from 'pages/adminConsole/components/datastoreConfig'
import { DataStoreSecretGeneration } from 'pages/adminConsole/components/datastoreConfig/datastoreSecretGeneration'
import { DocumentSnapshots } from 'pages/adminConsole/components/documentSnapshots'
import { EISMetricsAndPolicies } from 'pages/adminConsole/components/eisAlertMetricsPolicies'
import { TrainingDataToolPage } from 'pages/adminConsole/components/training'
import * as React from 'react'
import { Redirect, Route, RouteComponentProps, Switch } from 'react-router'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { AccessReturnType } from 'shared/hooks'
import {
  FormBuilderAccessType,
  edcaRoutes,
  inboxManagerRoutes,
  routes,
  safeEdcaRoute,
} from 'shared/routes'
import { AgencyConfigDistrict, User } from 'static/graphql/types'
import { useUserContext } from 'store/context'
import { IncidentMerge } from 'iris-styleguide'
import { isEdcaContext } from 'shared/envUtils'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import { ListMembersForPartner, PartnerTeamListQuery } from '../queries'
import { Analytics } from './analytics'
import { AnnouncementBannerUpdate } from './announcementBannerUpdate'
import { AuditLog } from './auditLog'
import { CommandHierarchyTeams } from './commandHierarchyTeams'
import { Expungements } from './expungements'
import { Flags } from './flags'
import {
  AgencyForms,
  CustomLabels,
  EntityCustomizer,
  EntityViewSchemaCustomizer,
  FormBuilderPage,
  FormsDataPathHelper,
  LabelCustomizer,
  PrintSchemas,
  SubDocumentBuilderPage,
  SubDocumentDataPathHelper,
  SubDocumentPrintBuilderPage,
  SubDocumentPrintSchemas,
} from './forms'
import { MasterChargeTool } from './masterChargeTool'
import { MasterLocationTool } from './masterLocationTool'
import { MniDeduplication } from './mniDeduplication'
import { PartnerAttributes } from './partnerAttributes'
import { PropertyManagement } from './propertyManagement'
import { PersonnelToolPage } from './personnel'
import { SupervisorNoteAdminPage } from './supervisorNotesAdmin'
import { ReportSubmissionSettings } from './reportSubmissionSettings'
import { RuleManager } from './ruleManager/ruleManager'
import { ScopedSearch } from './scopedSearch'
import { TeamList } from './teamList'
import { UserAttributes } from './userAttributes'
import { UserManagement } from './userManagement'
import { WorkflowManager } from './workflowSetting'
import { FormBuilderContextProvider } from './forms/components/formBuilder/context'
import { InboxManager, InboxManagerProvider } from './inboxManager'
import { CreateInboxPage } from './inboxManager/createInboxPage'

type Props = {
  currentUser: User
  userAccess: AccessReturnType
  agencyDistricts?: Array<AgencyConfigDistrict>
}

// eslint-disable-next-line complexity
export function AdminPanel({
  agencyDistricts,
  currentUser,
  userAccess: {
    canAccessAuditLog,
    canAccessMasterChargeTable,
    canAccessMNIDedupe,
    canEditMasterLocationTool,
    viewAdminPage,
    viewExpungements,
    viewInternalPrivileges,
    viewFlagCategories,
    viewACL,
    canAccessRecordsDataStoreModule,
    canAccessStandardsDataStoreModule,
    canConfigureDataStore,
  },
}: Props) {
  const { hasAttribute, isLiteAgency, agencyPartnerId, hasPartnerFeature } = useUserContext()

  const renderWithRouter = (TabView: React.ComponentType<any>) => () => {
    return (
      <ListMembersForPartner forceFetch>
        {({ partnerUsers = [], partnerClients = [], refetch: listMemberRefetch }) => {
          return (
            <PartnerTeamListQuery forceFetch>
              {({ allTeams, refetch, loading }) => {
                return (
                  <TabView
                    allTeams={allTeams}
                    partnerUsers={partnerUsers}
                    partnerClients={partnerClients}
                    refetch={refetch}
                    currentUser={currentUser}
                    listMemberRefetch={listMemberRefetch}
                    agencyDistricts={agencyDistricts}
                    viewInternalPrivileges={viewInternalPrivileges}
                    allTeamsLoading={loading}
                  />
                )
              }}
            </PartnerTeamListQuery>
          )
        }}
      </ListMembersForPartner>
    )
  }
  const showMasterChargeTool =
    hasAttribute(AgencyAttributesEnums.enableMasterChargeTool) && canAccessMasterChargeTable
  const showMasterLocationTool =
    hasAttribute(AgencyAttributesEnums.enableMasterLocationTool) && canEditMasterLocationTool
  const showMergeTool = hasAttribute(AgencyAttributesEnums.mniDeduplication) && canAccessMNIDedupe
  const enableACL = hasAttribute(AgencyAttributesEnums.enableACL)
  const enableEISMetricBuilder = hasAttribute(AgencyAttributesEnums.enableEISMetricBuilder)
  const hasUserAttributes = hasAttribute(AgencyAttributesEnums.userAttributeConfigAdminPage)
  const hasTeamAttributes = hasAttribute(AgencyAttributesEnums.enableTeamAttribute)
  const allowLegacyFBRoutes = useGrantLegacyFormBuilderAccess(
    hasAttribute(AgencyAttributesEnums.enableFormBuilder)
  )
  const shouldShowInboxManager =
    hasPartnerFeature(PartnerFeatureEnums.RMSACCESS) &&
    hasAttribute(AgencyAttributesEnums.enableTaskInboxManager)

  const accessArr = [
    viewAdminPage,
    showMergeTool,
    showMasterChargeTool,
    canAccessAuditLog,
    showMasterLocationTool,
    canAccessRecordsDataStoreModule,
    canAccessStandardsDataStoreModule,
    canConfigureDataStore,
  ]

  if (accessArr.every((item) => !item)) {
    return <Redirect to={routes.ERROR.route({})} />
  }

  const getDefaultRoute = () => {
    switch (true) {
      case isEdcaContext():
        return safeEdcaRoute(edcaRoutes.ADMIN_FORMS.route, {
          formBuilderAccessType: FormBuilderAccessType.EFB,
          agencyPartnerId,
        })
      case viewAdminPage:
        return routes.ADMIN.route({ type: 'user-list' })
      case canAccessAuditLog:
        return routes.ADMIN.route({ type: 'audit' })
      case canAccessMasterChargeTable:
        return routes.ADMIN.route({ type: 'master-charge-tool' })
      case canEditMasterLocationTool:
        return routes.ADMIN.route({ type: 'master-location-tool' })
      case canConfigureDataStore:
        return routes.ADMIN.route({ type: 'datastore-settings' })
      case canAccessRecordsDataStoreModule:
      case canAccessStandardsDataStoreModule:
        return routes.ADMIN.route({ type: 'datastore-secret-generation' })
      default:
        return routes.ADMIN.route({ type: 'mni-merge-tool' })
    }
  }

  const defaultRoute = getDefaultRoute()

  if (isEdcaContext()) {
    return (
      <Switch>
        <Route
          exact
          path={edcaRoutes.ADMIN_FORMS.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(props) => (
            <AgencyForms
              {...props}
              key={FormBuilderAccessType.EFB}
              formBuilderAccessType={FormBuilderAccessType.EFB}
            />
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_FORM.path({ formBuilderAccessType: FormBuilderAccessType.EFB })}
          render={(
            props: Pick<React.ComponentProps<typeof FormBuilderPage>, keyof RouteComponentProps>
          ) => (
            <FormBuilderContextProvider formBuilderType={FormBuilderAccessType.EFB}>
              <FormBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />
            </FormBuilderContextProvider>
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_SUBFORMS.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_SUBFORM_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentDataPathHelper>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentDataPathHelper
              {...props}
              formBuilderAccessType={FormBuilderAccessType.EFB}
            />
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof PrintSchemas>, keyof RouteComponentProps>
          ) => <PrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof FormsDataPathHelper>, keyof RouteComponentProps>
          ) => <FormsDataPathHelper {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />}
        />
        {hasAttribute(AgencyAttributesEnums.enableCustomLabelEditor) && (
          <Route
            exact
            path={edcaRoutes.ADMIN_CUSTOM_LABELS.path({
              formBuilderAccessType: FormBuilderAccessType.EFB,
            })}
            render={(
              props: Pick<React.ComponentProps<typeof CustomLabels>, keyof RouteComponentProps>
            ) => <CustomLabels {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />}
          />
        )}
        <Route
          exact
          path={edcaRoutes.ADMIN_SUBFORM_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintSchemas>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_SUBFORM_PRINT.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintBuilderPage
              {...props}
              formBuilderAccessType={FormBuilderAccessType.EFB}
            />
          )}
        />
        <Route
          exact
          path={edcaRoutes.ADMIN_ENTITY_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof EntityCustomizer>, keyof RouteComponentProps>
          ) => <EntityCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />}
        />
        {hasAttribute(AgencyAttributesEnums.enableLabelsCustomization) && (
          <Route
            exact
            path={edcaRoutes.ADMIN_LABELS_ENTITY_CUSTOMIZATION.path({
              formBuilderAccessType: FormBuilderAccessType.EFB,
            })}
            render={(
              props: Pick<React.ComponentProps<typeof LabelCustomizer>, keyof RouteComponentProps>
            ) => <LabelCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.EFB} />}
          />
        )}
        <Route
          exact
          path={edcaRoutes.ADMIN_ENTITY_VIEW_SCHEMA_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.EFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof EntityViewSchemaCustomizer>,
              keyof RouteComponentProps
            >
          ) => (
            <EntityViewSchemaCustomizer
              {...props}
              formBuilderAccessType={FormBuilderAccessType.EFB}
            />
          )}
        />
      </Switch>
    )
  }
  return (
    <Switch>
      {hasAttribute(AgencyAttributesEnums.enableAnalytics) &&
        hasAttribute(AgencyAttributesEnums.enableAnalyticsAdminPage) && (
          <Route
            exact
            path={routes.ADMIN.route({ type: 'analytics' })}
            component={renderWithRouter(Analytics)}
          />
        )}
      <Route
        exact
        path={routes.ADMIN.route({ type: 'user-list' })}
        component={renderWithRouter(UserManagement)}
      />
      <Route
        exact
        path={routes.ADMIN.route({ type: 'team-list' })}
        component={renderWithRouter(TeamList)}
      />
      <Route
        exact
        path={routes.ADMIN.route({ type: 'command-hierarchy-teams' })}
        component={renderWithRouter(CommandHierarchyTeams)}
      />
      <Route exact path={routes.ADMIN.route({ type: 'audit' })} component={AuditLog} />
      <Route
        exact
        path={routes.ADMIN.route({ type: 'datastore-settings' })}
        component={DataStoreSettings}
      />
      <Route
        exact
        path={routes.ADMIN.route({ type: 'datastore-secret-generation' })}
        component={DataStoreSecretGeneration}
      />
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_FORMS.path({ formBuilderAccessType: FormBuilderAccessType.AFB })}
          render={(props) => (
            <AgencyForms
              {...props}
              // explicitly set key to force re-render when switching between AFB and LFB
              key={FormBuilderAccessType.AFB}
              formBuilderAccessType={FormBuilderAccessType.AFB}
            />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_FORMS.path({ formBuilderAccessType: FormBuilderAccessType.LFB })}
          render={(props) => (
            <AgencyForms
              {...props}
              // explicitly set key to force re-render when switching between AFB and LFB
              key={FormBuilderAccessType.LFB}
              formBuilderAccessType={FormBuilderAccessType.LFB}
            />
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_FORM.path({ formBuilderAccessType: FormBuilderAccessType.AFB })}
          render={(
            props: Pick<React.ComponentProps<typeof FormBuilderPage>, keyof RouteComponentProps>
          ) => (
            <FormBuilderContextProvider formBuilderType={FormBuilderAccessType.AFB}>
              <FormBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />
            </FormBuilderContextProvider>
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_FORM.path({ formBuilderAccessType: FormBuilderAccessType.LFB })}
          render={(
            props: Pick<React.ComponentProps<typeof FormBuilderPage>, keyof RouteComponentProps>
          ) => (
            <FormBuilderContextProvider formBuilderType={FormBuilderAccessType.LFB}>
              <FormBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />
            </FormBuilderContextProvider>
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_SUBFORMS.path({ formBuilderAccessType: FormBuilderAccessType.AFB })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_SUBFORMS.path({ formBuilderAccessType: FormBuilderAccessType.LFB })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentBuilderPage {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentDataPathHelper>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentDataPathHelper
              {...props}
              formBuilderAccessType={FormBuilderAccessType.AFB}
            />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentDataPathHelper>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentDataPathHelper
              {...props}
              formBuilderAccessType={FormBuilderAccessType.LFB}
            />
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof PrintSchemas>, keyof RouteComponentProps>
          ) => <PrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof PrintSchemas>, keyof RouteComponentProps>
          ) => <PrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof FormsDataPathHelper>, keyof RouteComponentProps>
          ) => <FormsDataPathHelper {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_DATA_PATH_HELPER.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof FormsDataPathHelper>, keyof RouteComponentProps>
          ) => <FormsDataPathHelper {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) &&
        hasAttribute(AgencyAttributesEnums.enableCustomLabelEditor) && (
          <Route
            exact
            path={routes.ADMIN_CUSTOM_LABELS.path({
              formBuilderAccessType: FormBuilderAccessType.AFB,
            })}
            render={(
              props: Pick<React.ComponentProps<typeof CustomLabels>, keyof RouteComponentProps>
            ) => <CustomLabels {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />}
          />
        )}
      {allowLegacyFBRoutes && hasAttribute(AgencyAttributesEnums.enableCustomLabelEditor) && (
        <Route
          exact
          path={routes.ADMIN_CUSTOM_LABELS.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof CustomLabels>, keyof RouteComponentProps>
          ) => <CustomLabels {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintSchemas>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_PRINT_SCHEMAS_LIST.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintSchemas>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintSchemas {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_PRINT.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintBuilderPage
              {...props}
              formBuilderAccessType={FormBuilderAccessType.AFB}
            />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_SUBFORM_PRINT.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof SubDocumentPrintBuilderPage>,
              keyof RouteComponentProps
            >
          ) => (
            <SubDocumentPrintBuilderPage
              {...props}
              formBuilderAccessType={FormBuilderAccessType.LFB}
            />
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_ENTITY_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof EntityCustomizer>, keyof RouteComponentProps>
          ) => <EntityCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_ENTITY_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof EntityCustomizer>, keyof RouteComponentProps>
          ) => <EntityCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) &&
        hasAttribute(AgencyAttributesEnums.enableLabelsCustomization) && (
          <Route
            exact
            path={routes.ADMIN_LABELS_ENTITY_CUSTOMIZATION.path({
              formBuilderAccessType: FormBuilderAccessType.AFB,
            })}
            render={(
              props: Pick<React.ComponentProps<typeof LabelCustomizer>, keyof RouteComponentProps>
            ) => <LabelCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.AFB} />}
          />
        )}
      {allowLegacyFBRoutes && hasAttribute(AgencyAttributesEnums.enableLabelsCustomization) && (
        <Route
          exact
          path={routes.ADMIN_LABELS_ENTITY_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<React.ComponentProps<typeof LabelCustomizer>, keyof RouteComponentProps>
          ) => <LabelCustomizer {...props} formBuilderAccessType={FormBuilderAccessType.LFB} />}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency) && (
        <Route
          exact
          path={routes.ADMIN_ENTITY_VIEW_SCHEMA_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.AFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof EntityViewSchemaCustomizer>,
              keyof RouteComponentProps
            >
          ) => (
            <EntityViewSchemaCustomizer
              {...props}
              formBuilderAccessType={FormBuilderAccessType.AFB}
            />
          )}
        />
      )}
      {allowLegacyFBRoutes && (
        <Route
          exact
          path={routes.ADMIN_ENTITY_VIEW_SCHEMA_CUSTOMIZATION.path({
            formBuilderAccessType: FormBuilderAccessType.LFB,
          })}
          render={(
            props: Pick<
              React.ComponentProps<typeof EntityViewSchemaCustomizer>,
              keyof RouteComponentProps
            >
          ) => (
            <EntityViewSchemaCustomizer
              {...props}
              formBuilderAccessType={FormBuilderAccessType.LFB}
            />
          )}
        />
      )}
      {showMasterChargeTool && !isLiteAgency && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'master-charge-tool' })}
          component={MasterChargeTool}
        />
      )}
      {showMasterLocationTool && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'master-location-tool' })}
          component={MasterLocationTool}
        />
      )}
      {hasUserAttributes && (
        <Route
          exact
          path={routes.ADMIN.route({
            type: hasTeamAttributes ? 'partner-attributes' : 'user-attributes',
          })}
          component={hasTeamAttributes ? PartnerAttributes : UserAttributes}
        />
      )}
      {hasTeamAttributes && !hasUserAttributes && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'partner-attributes' })}
          component={renderWithRouter(PartnerAttributes)}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableAnnouncementBannerSelfUpdate) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'announcement-banner-update' })}
          component={AnnouncementBannerUpdate}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableClassificationsTool) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'classifications-tool' })}
          component={
            enableACL && viewACL
              ? renderWithRouter(ClassificationToolExceptions)
              : renderWithRouter(ClassificationsTool)
          }
        />
      )}
      {showMergeTool && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'mni-merge-tool' })}
          component={MniDeduplication}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableIncidentMerge) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'incident-merge-tool' })}
          component={IncidentMerge}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enablePep) && (
        <Route exact path={routes.ADMIN_PROPERTY_MANAGEMENT.path} component={PropertyManagement} />
      )}
      {hasAttribute(AgencyAttributesEnums.enableEIS) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'eis-policies' })}
          component={enableEISMetricBuilder ? EISMetricsAndPolicies : AlertPolicies}
        />
      )}
      {viewFlagCategories && !isLiteAgency && (
        <Route exact path={routes.ADMIN.route({ type: 'flags' })} component={Flags} />
      )}
      {(hasAttribute(AgencyAttributesEnums.enableWorkflowEditor) ||
        hasAttribute(AgencyAttributesEnums.enableNewWorkflowManager)) && (
        <Route
          exact
          path={
            hasAttribute(AgencyAttributesEnums.enableNewWorkflowManager)
              ? routes.ADMIN.route({ type: 'workflowManager' })
              : routes.ADMIN.route({ type: 'workflowSettings' })
          }
          component={renderWithRouter(WorkflowManager)}
        />
      )}
      {shouldShowInboxManager && (
        <Route
          exact
          path={routes.ADMIN.route({ type: inboxManagerRoutes.BASE })}
          render={(
            props: Pick<React.ComponentProps<typeof InboxManager>, keyof RouteComponentProps>
          ) => (
            <InboxManagerProvider>
              <InboxManager {...props} />
            </InboxManagerProvider>
          )}
        />
      )}
      {shouldShowInboxManager && (
        <Route
          exact
          path={routes.ADMIN_CREATE_INBOX.path}
          render={(
            props: Pick<React.ComponentProps<typeof CreateInboxPage>, keyof RouteComponentProps>
          ) => (
            <InboxManagerProvider>
              <CreateInboxPage {...props} />
            </InboxManagerProvider>
          )}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableRuleManager) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'ruleManager' })}
          component={renderWithRouter(RuleManager)}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableExpungementV2) && viewExpungements && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'expungements' })}
          component={renderWithRouter(Expungements)}
        />
      )}
      {hasAttribute(AgencyAttributesEnums.enableHardValidationFeature) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'reportSubmissionSettings' })}
          component={renderWithRouter(ReportSubmissionSettings)}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enablePersonnel) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'personnel-tool' })}
          component={PersonnelToolPage}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableTraining) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'training-data-tool' })}
          component={TrainingDataToolPage}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableSupervisorNotes) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'supervisor-notes' })}
          component={SupervisorNoteAdminPage}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableFullDocumentSnapshots) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'document-snapshots' })}
          component={DocumentSnapshots}
        />
      )}

      {hasAttribute(AgencyAttributesEnums.enableSearchScoping) && (
        <Route
          exact
          path={routes.ADMIN.route({ type: 'scoped-search' })}
          component={ScopedSearch}
        />
      )}

      <Route exact path={routes.ADMIN.route({ type: 'api-attributes' })} />
      <Route path={routes.ADMIN.path} component={() => <Redirect to={defaultRoute} />} />
    </Switch>
  )
}
