import * as React from 'react'
import { render, screen } from 'test/utils'
import { mockUseUserAccess, mockUseUserContext } from 'test/mocks/userContext'
import moment from 'moment'
import { formatDateTime } from '@axon-enterprise/formatters'
import { RmsModuleTypeEnum, User } from 'static/graphql/types'
import { getUser } from 'test/mocks/user'
import * as userHooks from './queries/useGetSelfDataStoreUserCredentialStatus'
import * as datastoreConfigHooks from './queries/useDataStoreConfigQuery'
import { DataStoreSecretGeneration } from './datastoreSecretGeneration'
import { SecretStatus } from './datatypes'

const addToastSpy = jest.fn()
jest.mock('@axon-enterprise/spark', () => ({
  ...jest.requireActual('@axon-enterprise/spark'),
  useToast: () => addToastSpy,
}))
const userMockResults = {
  loading: false,
  data: {
    userCredentialStatus: {
      userId: '00000000-0000-0000-0000-000000000000',
      username: 'username',
      timeToLive: 24,
      secretStatus: SecretStatus.Active,
      generatedAt: new Date(),
      expiredAt: moment().add(24, 'hours'),
      isNeverExpired: false,
    },
    databaseInformation: {
      databaseName: 'database',
      serverName: 'server',
    },
  },
  refetch: jest.fn(),
}

const configMockResults = {
  loading: false,
  config: {
    maxTimeToLive: 24,
    allowNoExpiration: false,
    ips: [],
  },
  refetch: jest.fn(),
}
describe('DataStoreSecretGeneration', () => {
  beforeEach(() => {
    jest
      .spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus')
      .mockImplementation(() => userMockResults)

    jest
      .spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery')
      .mockImplementation(() => configMockResults)

    mockUseUserAccess({
      overrides: {
        canAccessRecordsDataStoreModule: true,
        canAccessStandardsDataStoreModule: true,
      },
    })
  })
  it('should show page when feature flag is on and both generation forms', () => {
    const { container } = render(<DataStoreSecretGeneration />)

    screen.getByText(/DataStore Secret Generation/i)

    expect(screen.getByText(/RECORDS DataStore - Access Information/i)).toBeDefined()
    expect(screen.getByText(/Secret Generator for RECORDS DataStore/i)).toBeDefined()
    expect(screen.getAllByText('The maximum Time-To-Live is 24 hours')).toBeDefined()

    expect(screen.getByText(/STANDARDS DataStore - Access Information/i)).toBeDefined()
    expect(screen.getByText(/Secret Generator for STANDARDS DataStore/i)).toBeDefined()

    expect(container).toHaveTextContent(/Server: server/i)
    expect(container).toHaveTextContent(/Database: database/i)
    expect(container).toHaveTextContent(/Login ID: username/i)
    expect(container).toHaveTextContent(
      `expired at ${formatDateTime(userMockResults.data.userCredentialStatus.expiredAt)}`
    )
  })

  it('should show one secret generation form with Records access', () => {
    mockUseUserAccess({
      overrides: {
        canAccessRecordsDataStoreModule: true,
        canAccessStandardsDataStoreModule: false,
      },
    })

    render(<DataStoreSecretGeneration />)

    expect(screen.getByText(/RECORDS DataStore - Access Information/i)).toBeDefined()
    expect(screen.getByText(/Secret Generator for RECORDS DataStore/i)).toBeDefined()

    expect(screen.queryByText(/STANDARDS DataStore - Access Information/i)).toBeNull()
  })

  it('should show correct status when secret is expired ', () => {
    mockUseUserAccess({
      overrides: {
        canAccessRecordsDataStoreModule: true,
        canAccessStandardsDataStoreModule: false,
      },
    })
    const mock = {
      ...userMockResults,
      data: {
        userCredentialStatus: {
          secretStatus: SecretStatus.Expired,
        },
      },
    }
    jest.spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus').mockImplementation(() => mock)

    const { container } = render(<DataStoreSecretGeneration />)

    expect(container).toHaveTextContent('Secret Expiration: Expired')
  })

  it('should show one secret generation form with Standards access', () => {
    mockUseUserAccess({
      overrides: {
        canAccessRecordsDataStoreModule: false,
        canAccessStandardsDataStoreModule: true,
      },
    })

    render(<DataStoreSecretGeneration />)

    expect(screen.getByText(/STANDARDS DataStore - Access Information/i)).toBeDefined()
    expect(screen.getByText(/Secret Generator for STANDARDS DataStore/i)).toBeDefined()

    expect(screen.queryByText(/RECORDS DataStore - Access Information/i)).toBeNull()
  })

  it('should use username in userCredentialStatus', () => {
    const { container } = render(<DataStoreSecretGeneration />)

    expect(container).toHaveTextContent(/Login ID: username/)
  })

  it('should use ecom username by default if userCredentialStatus not return username', async () => {
    // Empty username from API userCredentialStatus
    const mock = {
      ...userMockResults,
      data: {
        userCredentialStatus: {
          username: '',
        },
      },
    }

    jest.spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus').mockImplementation(() => mock)

    const testUser: User = getUser({
      overrides: {
        username: 'ecom_username',
      },
    })
    mockUseUserContext({ overrides: { user: testUser } })

    const { container } = render(<DataStoreSecretGeneration />)
    expect(container).toHaveTextContent(/Login ID: ecom_username/)
  })

  it('should use username from userCredentialStatus even ecom username exits', async () => {
    // Empty username from API userCredentialStatus
    const mock = {
      ...userMockResults,
      data: {
        userCredentialStatus: {
          username: 'api_username',
        },
      },
    }

    jest.spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus').mockImplementation(() => mock)

    const testUser: User = getUser({
      overrides: {
        username: 'ecom_username',
      },
    })
    mockUseUserContext({ overrides: { user: testUser } })

    const { container } = render(<DataStoreSecretGeneration />)
    expect(container).toHaveTextContent(/Login ID: api_username/)
  })

  it('should show secret has not been generated for new user', () => {
    const mock = {
      ...userMockResults,
      data: {
        userCredentialStatus: {
          secretStatus: SecretStatus.Inactive,
        },
      },
    }

    jest.spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus').mockImplementation(() => mock)

    const { container } = render(<DataStoreSecretGeneration />)
    expect(container).toHaveTextContent(/This secret hasn't been generated yet/)
  })

  it('should show message never expires if field isNeverExpired is true', () => {
    const mock = {
      ...userMockResults,
      data: {
        userCredentialStatus: {
          secretStatus: SecretStatus.Active,
          isNeverExpired: true,
        },
      },
    }

    jest.spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus').mockImplementation(() => mock)

    const { container } = render(<DataStoreSecretGeneration />)
    expect(container).toHaveTextContent(/Secret Expiration: This secret never expires/)
  })

  describe('configIsNotSetup', () => {
    it('should not show Records secret generation section if config is null, even have permisison', async () => {
      jest
        .spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery')
        .mockImplementation(({ module }) => {
          if (module === RmsModuleTypeEnum.Standards) {
            return configMockResults
          }
          if (module === RmsModuleTypeEnum.Records) {
            return { ...configMockResults, config: undefined }
          }
          return configMockResults
        })
      render(<DataStoreSecretGeneration />)
      expect(screen.queryByText(/Secret Generator for RECORDS DataStore/i)).toBeNull()
      expect(screen.queryByText(/RECORDS DataStore - Access Information/i)).toBeNull()
      expect(screen.getByText(/STANDARDS DataStore - Access Information/i)).toBeDefined()
      expect(screen.getByText(/Secret Generator for STANDARDS DataStore/i)).toBeDefined()
    })

    it('should not show Standards secret generation section if config is null, even have permisison', async () => {
      jest
        .spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery')
        .mockImplementation(({ module }) => {
          if (module === RmsModuleTypeEnum.Records) {
            return configMockResults
          }
          if (module === RmsModuleTypeEnum.Standards) {
            return { ...configMockResults, config: undefined }
          }
          return configMockResults
        })
      render(<DataStoreSecretGeneration />)
      expect(screen.queryByText(/Secret Generator for STANDARDS DataStore/i)).toBeNull()
      expect(screen.queryByText(/STANDARDS DataStore - Access Information/i)).toBeNull()
      expect(screen.getByText(/RECORDS DataStore - Access Information/i)).toBeDefined()
      expect(screen.getByText(/Secret Generator for RECORDS DataStore/i)).toBeDefined()
    })

    it('should show poster no secret been generated yet if all configs are null, even have permisison', async () => {
      jest.spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery').mockImplementation(() => {
        return { ...configMockResults, config: undefined }
      })
      render(<DataStoreSecretGeneration />)
      expect(screen.queryByText(/Secret Generator for STANDARDS DataStore/i)).toBeNull()
      expect(screen.queryByText(/STANDARDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/RECORDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/Secret Generator for RECORDS DataStore/i)).toBeNull()

      expect(screen.getByText(/No secrets have been generated yet/i)).toBeDefined()
      expect(
        screen.getByText(
          /The Records and Standards DataStores have not been configured for your organization. Contact your admin or Axon representative for assistance./i
        )
      ).toBeDefined()
    })

    it('should show poster no secret been generated yet if only have permission to Records and the config is null', async () => {
      mockUseUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: true,
          canAccessStandardsDataStoreModule: false,
        },
      })
      jest.spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery').mockImplementation(() => {
        return { ...configMockResults, config: undefined }
      })
      render(<DataStoreSecretGeneration />)
      expect(screen.queryByText(/Secret Generator for STANDARDS DataStore/i)).toBeNull()
      expect(screen.queryByText(/STANDARDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/RECORDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/Secret Generator for RECORDS DataStore/i)).toBeNull()

      expect(screen.getByText(/No secrets have been generated yet/i)).toBeDefined()
      expect(
        screen.getByText(
          /The Records and Standards DataStores have not been configured for your organization. Contact your admin or Axon representative for assistance./i
        )
      ).toBeDefined()
    })

    it('should show poster no secret been generated yet if only have permission to Standards and the config is null', async () => {
      mockUseUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: false,
          canAccessStandardsDataStoreModule: true,
        },
      })
      jest.spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery').mockImplementation(() => {
        return { ...configMockResults, config: undefined }
      })
      render(<DataStoreSecretGeneration />)
      expect(screen.queryByText(/Secret Generator for STANDARDS DataStore/i)).toBeNull()
      expect(screen.queryByText(/STANDARDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/RECORDS DataStore - Access Information/i)).toBeNull()
      expect(screen.queryByText(/Secret Generator for RECORDS DataStore/i)).toBeNull()

      expect(screen.getByText(/No secrets have been generated yet/i)).toBeDefined()
      expect(
        screen.getByText(
          /The Records and Standards DataStores have not been configured for your organization. Contact your admin or Axon representative for assistance./i
        )
      ).toBeDefined()
    })
  })

  describe('functionWithPrivileges', () => {
    it('should not call Standards DAC config if not have access to Standards', () => {
      mockUseUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: true,
          canAccessStandardsDataStoreModule: false,
        },
      })
      const datastoreConfigHooksSpy = jest.spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery')
      render(<DataStoreSecretGeneration />)
      expect(datastoreConfigHooksSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          module: RmsModuleTypeEnum.Records,
        })
      )
      expect(datastoreConfigHooksSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          module: RmsModuleTypeEnum.Standards,
          enabled: false,
        })
      )
    })

    it('should not call Records DAC config if not have access to Records', () => {
      mockUseUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: false,
          canAccessStandardsDataStoreModule: true,
        },
      })
      const datastoreConfigHooksSpy = jest.spyOn(datastoreConfigHooks, 'useDataStoreConfigQuery')
      render(<DataStoreSecretGeneration />)
      expect(datastoreConfigHooksSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          module: RmsModuleTypeEnum.Standards,
        })
      )
      expect(datastoreConfigHooksSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          module: RmsModuleTypeEnum.Records,
          enabled: false,
        })
      )
    })
  })

  describe('syncDACAccount', () => {
    it('should show secret has not been genarated when secretStatus is NotCreated', () => {
      const mock = {
        ...userMockResults,
        data: {
          userCredentialStatus: {
            secretStatus: SecretStatus.NotCreated,
            isNeverExpired: true,
          },
        },
      }
      jest
        .spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus')
        .mockImplementation(() => mock)
      const { container } = render(<DataStoreSecretGeneration />)
      expect(container).toHaveTextContent(/This secret hasn't been generated yet/)
    })

    it('should show account not sync when userCredentialStatus is undefined', () => {
      const mock = {
        ...userMockResults,
        data: {
          ...userMockResults.data,
          userCredentialStatus: undefined,
        },
      }
      const testUser: User = getUser({
        overrides: {
          username: 'ecom_username',
        },
      })
      mockUseUserContext({ overrides: { user: testUser } })
      jest
        .spyOn(userHooks, 'useGetSelfDataStoreUserCredentialStatus')
        .mockImplementation(() => mock)
      const { container } = render(<DataStoreSecretGeneration />)

      expect(container).toHaveTextContent(/Server: server/i)
      expect(container).toHaveTextContent(/Database: database/i)
      // Should use ecom username
      expect(container).toHaveTextContent(/Login ID: ecom_username/i)
      expect(container).toHaveTextContent(
        /Your Axon Evidence account hasn't synced to the DataStore yet./
      )
      expect(container).not.toHaveTextContent(/Secret Generator for RECORDS DataStore/)
    })
  })
})
