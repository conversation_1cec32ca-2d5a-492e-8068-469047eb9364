import * as envUtils from 'shared/envUtils'
import { log } from 'shared/log'

import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { createBrowserHistory } from 'history'
import { getUserAccess } from 'test/mocks/userAccess'
import { FormBuilderAccessType, edcaRoutes, safeEdcaRoute } from 'shared/routes'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import { getNavItems, hasDistrict } from './pageSettings'

describe('getNavItems', () => {
  const access = getUserAccess()
  const districtLabelValue = 'blah'
  const districtFallbackTitle = 'second choice label'
  const userAttributeSchema = {
    id: 'this is my id',
  }
  const history = createBrowserHistory()
  const grantLegacyFormBuilderAccess = false
  const isLiteAgency = false
  const agencyPartnerId = 'test-agency-123'
  const expectedRoute = safeEdcaRoute(edcaRoutes.ADMIN_FORMS.route, {
    agencyPartnerId,
    formBuilderAccessType: FormBuilderAccessType.EFB,
  })
  beforeEach(() => {
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)
  })

  it('should return the workflow settings nav item if enableNewWorkflowManager is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) =>
        attr !== AgencyAttributesEnums.enableNewWorkflowManager &&
        attr === AgencyAttributesEnums.enableWorkflowEditor,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Workflow Settings',
        shouldShow: true,
        route: 'workflowSettings',
      },
    ])
  })

  it('should return the workflow manager nav item if enableNewWorkflowManager is enabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) =>
        attr === AgencyAttributesEnums.enableNewWorkflowManager ||
        attr === AgencyAttributesEnums.enableWorkflowEditor,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Workflow Manager',
        shouldShow: true,
        route: 'workflowManager',
      },
    ])
  })

  it('should return the inbox manager nav item if enableInboxManager and RMSAccess is enabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableTaskInboxManager,
      hasPartnerFeature: (attr) => attr === PartnerFeatureEnums.RMSACCESS,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Inbox Manager',
        shouldShow: true,
        route: 'inbox-manager',
      },
    ])
  })

  it('should not return the inbox manager nav item if enableInboxManager is enabled and RMSAccess is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableTaskInboxManager,
      hasPartnerFeature: (attr) => attr !== PartnerFeatureEnums.RMSACCESS,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Inbox Manager',
        shouldShow: true,
        route: 'inbox-manager',
      }),
    ])
  })

  it('should not return the inbox manager nav item if enableInboxManager is disabled and RMSAccess is enabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableTaskInboxManager,
      hasPartnerFeature: (attr) => attr === PartnerFeatureEnums.RMSACCESS,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Inbox Manager',
        shouldShow: true,
        route: 'inbox-manager',
      }),
    ])
  })

  it('should not return the EIS Management nav item if viewAdminPage is prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: false } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableEIS,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'EIS Management',
        shouldShow: true,
        route: 'eis-policies',
      }),
    ])
  })

  it('should not return the EIS Management nav item if enableEIS is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableEIS,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'EIS Management',
        shouldShow: true,
        route: 'eis-policies',
      }),
    ])
  })

  it('should return EIS Management nav item if viewAdminPage is allowed and enableEIS is enabled', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: true } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableEIS,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'EIS Management',
        shouldShow: true,
        route: 'eis-policies',
      }),
    ])
  })

  it('should not return the Report Submission Settings nav item if viewAdminPage is prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: false } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableHardValidationFeature,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Report Submission Settings',
        shouldShow: true,
        route: 'reportSubmissionSettings',
      }),
    ])
  })

  it('should not return the Report Submission Settings nav item if enableHardValidationFeature is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableHardValidationFeature,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Report Submission Settings',
        shouldShow: true,
        route: 'reportSubmissionSettings',
      }),
    ])
  })

  it('should return Report Submission Settings nav item if viewAdminPage is allowed and enableHardValidationFeature is enabled', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: true } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableHardValidationFeature,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Report Submission Settings',
        shouldShow: true,
        route: 'reportSubmissionSettings',
      }),
    ])
  })

  it('should not return the Document Snapshots nav item if viewAdminPage is prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: false } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableFullDocumentSnapshots,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Document Snapshots',
        shouldShow: true,
        route: 'document-snapshots',
      }),
    ])
  })

  it('should not return the Document Snapshots nav item if enableFullDocumentSnapshots is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableFullDocumentSnapshots,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Document Snapshots',
        shouldShow: true,
        route: 'document-snapshots',
      }),
    ])
  })

  it('should return the Document Snapshots nav item if viewAdminPage is allowed and enableFullDocumentSnapshots is enabled', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: true } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableFullDocumentSnapshots,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Document Snapshots',
        shouldShow: true,
        route: 'document-snapshots',
      }),
    ])
  })

  it('should not return the Scoped Search nav item if viewAdminPage is prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: false } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableSearchScoping,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Scoped Search',
        shouldShow: true,
        route: 'scoped-search',
      }),
    ])
  })

  it('should not return the Scoped Search nav item if enableSearchScoping is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableSearchScoping,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Scoped Search',
        shouldShow: true,
        route: 'scoped-search',
      }),
    ])
  })

  it('should return the Scoped Search nav item if viewAdminPage is allowed and enableSearchScoping is enabled', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { viewAdminPage: true } }),
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableSearchScoping,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Scoped Search',
        shouldShow: true,
        route: 'scoped-search',
      }),
    ])
  })

  it('should not return the DataStore Settings nav item if canConfigureDataStore is prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { canConfigureDataStore: false } }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Settings',
        shouldShow: true,
        route: 'datastore-settings',
      }),
    ])
  })

  it('should return the DataStore Settings nav item if canConfigureDataStore is allowed', () => {
    const navItems = getNavItems({
      access: getUserAccess({ overrides: { canConfigureDataStore: true } }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Settings',
        shouldShow: true,
        route: 'datastore-settings',
      }),
    ])
  })

  it('should not return the DataStore Secret Generation nav item if canAccessRecordsDataStoreModule and canAccessStandardsDataStoreModule are prohibited', () => {
    const navItems = getNavItems({
      access: getUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: false,
          canAccessStandardsDataStoreModule: false,
        },
      }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Secret Generation',
        shouldShow: true,
        route: 'datastore-secret-generation',
      }),
    ])
  })

  it('should return the DataStore Secret Generation nav item if canAccessRecordsDataStoreModule and canAccessStandardsDataStoreModule are allowed', () => {
    const navItems = getNavItems({
      access: getUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: true,
          canAccessStandardsDataStoreModule: true,
        },
      }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Secret Generation',
        shouldShow: true,
        route: 'datastore-secret-generation',
      }),
    ])
  })

  it('should return the DataStore Secret Generation nav item if canAccessRecordsDataStoreModule is true, canAccessStandardsDataStoreModule is false', () => {
    const navItems = getNavItems({
      access: getUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: true,
          canAccessStandardsDataStoreModule: false,
        },
      }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Secret Generation',
        shouldShow: true,
        route: 'datastore-secret-generation',
      }),
    ])
  })

  it('should return the DataStore Secret Generation nav item if canAccessRecordsDataStoreModule is false, canAccessStandardsDataStoreModule is true', () => {
    const navItems = getNavItems({
      access: getUserAccess({
        overrides: {
          canAccessRecordsDataStoreModule: false,
          canAccessStandardsDataStoreModule: true,
        },
      }),
      hasAttribute: () => true,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      expect.objectContaining({
        label: 'DataStore Secret Generation',
        shouldShow: true,
        route: 'datastore-secret-generation',
      }),
    ])
  })

  it('should return the master location tool nav item if enableMasterLocationTool is enabled', () => {
    const locationToolAccess = getUserAccess({ overrides: { canEditMasterLocationTool: true } })
    const navItems = getNavItems({
      access: locationToolAccess,
      hasAttribute: (attr) =>
        attr === AgencyAttributesEnums.enableMasterLocationTool ||
        attr === AgencyAttributesEnums.enableWorkflowEditor,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Master Location Tool',
        shouldShow: true,
        route: 'master-location-tool',
      },
    ])
  })

  it('should return the property management nav item', () => {
    const viewAdminPageAccess = getUserAccess({ overrides: { viewAdminPage: true } })

    const navItems = getNavItems({
      access: viewAdminPageAccess,
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enablePep,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Property Management',
        shouldShow: true,
        route: 'property-management',
      },
    ])
  })

  it('should return the Legacy FormBuilder nav item if grantLegacyFormBuilderAccess is true', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess: true,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Form Builder (Legacy)',
        shouldShow: true,
        route: 'legacy-forms',
      },
    ])
  })

  it('should not return the Legacy FormBuilder nav item if grantLegacyFormBuilderAccess is false', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toIncludeAnyMembers([
      {
        label: 'Form Builder (Legacy)',
        shouldShow: false,
        route: 'legacy-forms',
      },
    ])
  })

  it('should return the Agency FormBuilder nav item if enableFormBuilderAgency is enabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr === AgencyAttributesEnums.enableFormBuilderAgency,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          label: 'Form Builder (Early Access)',
          shouldShow: true,
          route: 'forms',
        }),
      ])
    )
  })

  it('should not return the Agency FormBuilder nav item if enableFormBuilderAgency is disabled', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: (attr) => attr !== AgencyAttributesEnums.enableFormBuilderAgency,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    expect(navItems).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          label: 'Form Builder (Early Access)',
          shouldShow: false,
          route: 'forms',
        }),
      ])
    )
  })

  it('should track analytics and navigate when AFB FormBuilder onClick is called', () => {
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(false)
    const logTrackSpy = jest.spyOn(log, 'track').mockImplementation()
    const historyPushSpy = jest.spyOn(history, 'push').mockImplementation()

    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
    })

    const afbFormBuilderItem = navItems.find((item) => item.label === 'Form Builder (Early Access)')
    expect(afbFormBuilderItem).toBeDefined()
    expect(afbFormBuilderItem?.onClick).toBeDefined()

    // Call the onClick function
    afbFormBuilderItem?.onClick?.()

    // Verify tracking call
    expect(logTrackSpy).toHaveBeenCalledWith('fb/form-builder-early-access-link')
    expect(logTrackSpy).toHaveBeenCalledTimes(1)

    // Verify navigation call
    expect(historyPushSpy).toHaveBeenCalledWith('forms')
    expect(historyPushSpy).toHaveBeenCalledTimes(1)

    // Clean up spies
    logTrackSpy.mockRestore()
    historyPushSpy.mockRestore()
  })

  it('should not see the Edca FormBuilder nav item if not in EdcaContext', () => {
    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
      agencyPartnerId,
    })

    expect(navItems).not.toIncludeAnyMembers([
      expect.objectContaining({
        label: 'Form Builder (EDCA)',
        shouldShow: true,
        route: 'forms',
      }),
      expect.objectContaining({
        label: 'Form Builder (EDCA)',
        shouldShow: false,
        route: 'forms',
      }),
    ])
  })

  it('should see only the Edca FormBuilder nav item if in EdcaContext', () => {
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)
    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
      agencyPartnerId,
    })

    expect(navItems).toIncludeSameMembers([
      expect.objectContaining({
        label: 'Form Builder (EDCA)',
        shouldShow: true,
        route: expectedRoute,
      }),
    ])
  })

  it('should track analytics and navigate when EDCA FormBuilder onClick is called', () => {
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)
    const logTrackSpy = jest.spyOn(log, 'track').mockImplementation()
    const historyPushSpy = jest.spyOn(history, 'push').mockImplementation()

    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
      agencyPartnerId,
    })

    const edcaFormBuilderItem = navItems.find((item) => item.label === 'Form Builder (EDCA)')
    expect(edcaFormBuilderItem).toBeDefined()
    expect(edcaFormBuilderItem?.onClick).toBeDefined()

    // Call the onClick function
    edcaFormBuilderItem?.onClick?.()

    // Verify tracking call
    expect(logTrackSpy).toHaveBeenCalledWith('fb/form-builder-efb-access-link')
    expect(logTrackSpy).toHaveBeenCalledTimes(1)

    // Verify navigation call
    expect(historyPushSpy).toHaveBeenCalledWith(expectedRoute)
    expect(historyPushSpy).toHaveBeenCalledTimes(1)

    // Clean up spies
    logTrackSpy.mockRestore()
    historyPushSpy.mockRestore()
  })

  it('should use error route when agencyPartnerId is undefined in EDCA FormBuilder', () => {
    jest.spyOn(envUtils, 'isEdcaContext').mockReturnValue(true)

    const logTrackSpy = jest.spyOn(log, 'track').mockImplementation()
    const historyPushSpy = jest.spyOn(history, 'push').mockImplementation()

    const errorRoute = safeEdcaRoute(edcaRoutes.ERROR.route, {
      agencyPartnerId: undefined,
      formBuilderAccessType: FormBuilderAccessType.EFB,
    })

    const navItems = getNavItems({
      access,
      hasAttribute: () => false,
      hasPartnerFeature: () => false,
      districtLabelValue,
      districtFallbackTitle,
      userAttributeSchema,
      history,
      grantLegacyFormBuilderAccess,
      isLiteAgency,
      agencyPartnerId: undefined,
    })

    const edcaFormBuilderItem = navItems.find((item) => item.label === 'Form Builder (EDCA)')
    expect(edcaFormBuilderItem).toBeDefined()

    // Verify the route is an error route
    expect(edcaFormBuilderItem?.route).toBe(errorRoute)
    expect(edcaFormBuilderItem?.route).toContain('/:agencyPartnerId/forms/error/400')
    expect(edcaFormBuilderItem?.route).toContain('No agency partner ID found')

    // Call the onClick function
    edcaFormBuilderItem?.onClick?.()

    // Verify tracking still occurs
    expect(logTrackSpy).toHaveBeenCalledWith('fb/form-builder-efb-access-link')
    expect(logTrackSpy).toHaveBeenCalledTimes(1)

    // Verify navigation goes to error route
    expect(historyPushSpy).toHaveBeenCalledWith(errorRoute)
    expect(historyPushSpy).toHaveBeenCalledWith(
      expect.stringContaining('/:agencyPartnerId/forms/error/400')
    )
    expect(historyPushSpy).toHaveBeenCalledTimes(1)

    // Clean up spies
    logTrackSpy.mockRestore()
    historyPushSpy.mockRestore()
  })
})

describe('hasDistrict', () => {
  it('should return true when userAttributeSchema has a district property', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: {
          title: 'District',
          type: 'string',
        },
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(true)
  })

  it('should return false when userAttributeSchema has a district property with null value', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: null,
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return true when userAttributeSchema has a district property with empty object', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: {},
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(true)
  })

  it('should return false when userAttributeSchema has no district property', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        otherProperty: 'value',
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return false when userAttributeSchema has no schema property', () => {
    const userAttributeSchema = {
      id: 'test-id',
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return false when userAttributeSchema schema is null', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: null,
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return false when userAttributeSchema schema is undefined', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: undefined,
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return true when district has complex nested structure', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: {
          title: 'District',
          type: 'object',
          properties: {
            name: { type: 'string' },
            code: { type: 'number' },
          },
        },
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(true)
  })

  it('should return false when district property has falsy but defined value like empty string', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: '',
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return false when district property has falsy but defined value like 0', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: 0,
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })

  it('should return false when district property has falsy but defined value like false', () => {
    const userAttributeSchema = {
      id: 'test-id',
      schema: {
        district: false,
      },
    }

    expect(hasDistrict(userAttributeSchema)).toBe(false)
  })
})
