/* eslint-disable max-lines */
/* eslint-disable complexity */
import { sortBy } from 'lodash'
import * as React from 'react'
import { useHistory } from 'react-router'
import { log } from 'shared/log'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { useConfiguredString } from 'shared/config/string'
import { AccessReturnType, useUserAccess } from 'shared/hooks'
import { NavContainerProps, PageSettings, moduleName } from 'shared/pageSettings'
import {
  FormBuilderAccessType,
  deprecatedRoutes,
  edcaRoutes,
  inboxManagerRoutes,
  routes,
  safeEdcaRoute,
} from 'shared/routes'
import { UserAccessEnum, UserAttributeSchema } from 'static/graphql/types'
import { useUserContext } from 'store/context'
import { isEdcaContext } from 'shared/envUtils'
import { PartnerFeatureEnums } from 'shared/partnerFeature'
import { useQueryUserAttributes } from './queries'
import { NavItemConfig } from './types'
import {
  isAllowedLegacyFormBuilderAccessSecure,
  isRestrictedUserDeniedLFBAccessSecure,
} from './components/forms/legacyUserAccess'

export const adminPageSettings: Partial<PageSettings> = {
  contentMargin: '0 0 0 XL',
  contentPadding: 'L',
  title: () => __('Admin'),
  backgroundColor: 'white',
  module: moduleName.adminConsole,
  urlRoot: deprecatedRoutes.APP_ADMIN,
  NavContainer,
}

export function hasDistrict(userAttributeSchema: UserAttributeSchema) {
  return !!userAttributeSchema?.schema?.district
}

function useAllowedLegacyFormBuilderAccess() {
  const { user } = useUserContext()
  if (!user.email || !user.partnerId) {
    return false
  }
  return isAllowedLegacyFormBuilderAccessSecure(user.email, user.partnerId)
}

function useOverrideAndDenyLegacyFormBuilderAccess() {
  const { user } = useUserContext()
  if (!user.email || !user.partnerId) {
    return true
  }
  return isRestrictedUserDeniedLFBAccessSecure(user.email, user.partnerId)
}

export function useGrantLegacyFormBuilderAccess(enableFormBuilder: boolean) {
  const validLFBUser = useAllowedLegacyFormBuilderAccess()
  const overrideAndDenyLFBAccess = useOverrideAndDenyLegacyFormBuilderAccess()
  return enableFormBuilder || (!overrideAndDenyLFBAccess && validLFBUser)
}

export function getNavItems({
  access,
  hasAttribute,
  hasPartnerFeature,
  districtLabelValue,
  districtFallbackTitle,
  userAttributeSchema,
  history,
  grantLegacyFormBuilderAccess,
  isLiteAgency,
  agencyPartnerId,
}: {
  access: AccessReturnType
  hasAttribute: (attribute: AgencyAttributesEnums) => boolean
  hasPartnerFeature: (feature: PartnerFeatureEnums) => boolean
  districtLabelValue: string | undefined
  districtFallbackTitle: string
  userAttributeSchema: UserAttributeSchema
  history: any
  grantLegacyFormBuilderAccess: boolean
  isLiteAgency: boolean
  agencyPartnerId?: string
}): NavItemConfig[] {
  const shouldShowInboxManager =
    access.viewAdminPage &&
    hasPartnerFeature(PartnerFeatureEnums.RMSACCESS) &&
    hasAttribute(AgencyAttributesEnums.enableTaskInboxManager)
  if (isEdcaContext()) {
    return sortBy(
      [
        {
          label: __('Form Builder (EDCA)'),
          shouldShow: access.viewAdminPage,
          route: safeEdcaRoute(edcaRoutes.ADMIN_FORMS.route, {
            agencyPartnerId,
            formBuilderAccessType: FormBuilderAccessType.EFB,
          }),
          onClick: () => {
            log.track('fb/form-builder-efb-access-link')
            history.push(
              safeEdcaRoute(edcaRoutes.ADMIN_FORMS.route, {
                agencyPartnerId,
                formBuilderAccessType: FormBuilderAccessType.EFB,
              })
            )
          },
        },
      ],
      'label'
    )
  }

  return sortBy(
    [
      {
        label: __('Analytics'),
        shouldShow:
          access.viewAdminPage &&
          hasAttribute(AgencyAttributesEnums.enableAnalytics) &&
          hasAttribute(AgencyAttributesEnums.enableAnalyticsAdminPage),
        route: 'analytics',
      },
      {
        label: __('User Management'),
        shouldShow: access.viewAdminPage,
        route: 'user-list',
      },
      {
        label: __('Teams'),
        shouldShow:
          access.viewAdminPage && !hasAttribute(AgencyAttributesEnums.enableCommandHierarchyInRMS),
        route: 'team-list',
      },
      {
        label: __('Command Hierarchy & Teams'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableCommandHierarchyInRMS),
        route: 'command-hierarchy-teams',
      },
      {
        label: __('Audit Log Tool'),
        shouldShow: access.viewAdminPage || access.canAccessAuditLog,
        route: 'audit',
      },
      {
        label: __('Form Builder (Legacy)'),
        shouldShow: access.viewAdminPage && grantLegacyFormBuilderAccess,
        route: 'legacy-forms',
      },
      {
        label: __('Form Builder (Early Access)'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableFormBuilderAgency),
        route: 'forms',
        onClick: () => {
          log.track('fb/form-builder-early-access-link')
          history.push('forms')
        },
      },
      {
        label: __('Master Charge Tool'),
        shouldShow:
          access.canAccessMasterChargeTable &&
          hasAttribute(AgencyAttributesEnums.enableMasterChargeTool) &&
          !isLiteAgency,
        route: 'master-charge-tool',
      },
      {
        label: __('Master Location Tool'),
        shouldShow:
          access.canEditMasterLocationTool &&
          hasAttribute(AgencyAttributesEnums.enableMasterLocationTool),
        route: 'master-location-tool',
      },
      {
        label: districtLabelValue || districtFallbackTitle,
        shouldShow:
          access.viewAdminPage &&
          !hasAttribute(AgencyAttributesEnums.enableTeamAttribute) &&
          hasAttribute(AgencyAttributesEnums.userAttributeConfigAdminPage) &&
          hasDistrict(userAttributeSchema),
        route: 'user-attributes',
      },
      {
        label: districtLabelValue || districtFallbackTitle,
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableTeamAttribute),
        route: 'partner-attributes',
      },
      {
        label: __('Announcement Banner Update'),
        shouldShow:
          access.viewAdminPage &&
          hasAttribute(AgencyAttributesEnums.enableAnnouncementBannerSelfUpdate),
        route: 'announcement-banner-update',
      },
      {
        label:
          access.viewACL && hasAttribute(AgencyAttributesEnums.enableACL)
            ? __('Restrictions')
            : __('Classifications Tool'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableClassificationsTool),
        route: 'classifications-tool',
      },
      {
        label: __('MNI Merge Tool'),
        shouldShow:
          access.canAccessMNIDedupe && hasAttribute(AgencyAttributesEnums.mniDeduplication),
        route: 'mni-merge-tool',
      },
      {
        label: __('Incident Merge Tool'),
        // TODO: add permission check after RMS-53905 is complete
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableIncidentMerge),
        route: 'incident-merge-tool',
      },
      {
        label: __('Personnel Data Tool'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enablePersonnel),
        route: 'personnel-tool',
      },
      {
        label: __('Training Data Tool'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableTraining),
        route: 'training-data-tool',
      },
      {
        label: __('Feedback Note'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableSupervisorNotes),
        route: 'supervisor-notes',
      },
      {
        label: __('Property Management'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enablePep),
        route: 'property-management',
      },
      {
        label: __('Flags Configuration'),
        shouldShow: access.viewAdminPage && access.viewFlagCategories && !isLiteAgency,
        route: 'flags',
      },
      {
        label: hasAttribute(AgencyAttributesEnums.enableNewWorkflowManager)
          ? __('Workflow Manager')
          : __('Workflow Settings'),
        shouldShow:
          access.viewAdminPage &&
          (hasAttribute(AgencyAttributesEnums.enableWorkflowEditor) ||
            hasAttribute(AgencyAttributesEnums.enableNewWorkflowManager)),
        route: hasAttribute(AgencyAttributesEnums.enableNewWorkflowManager)
          ? 'workflowManager'
          : 'workflowSettings',
      },
      {
        label: __('Rule Manager'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableRuleManager),
        route: 'ruleManager',
      },
      {
        label: __('Expungements'),
        shouldShow:
          access.viewAdminPage &&
          access.viewExpungements &&
          hasAttribute(AgencyAttributesEnums.enableExpungementV2),
        route: 'expungements',
      },
      {
        label: __('Dev Settings'),
        shouldShow: hasAttribute(AgencyAttributesEnums.enableDevSettings),
        route: 'devSettings',
        onClick: () => history.push(routes.DEV_SETTINGS.route()),
      },
      {
        label: __('EIS Management'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableEIS),
        route: 'eis-policies',
      },
      {
        label: __('Report Submission Settings'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableHardValidationFeature),
        route: 'reportSubmissionSettings',
      },
      {
        label: __('Document Snapshots'),
        shouldShow:
          access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableFullDocumentSnapshots),
        route: 'document-snapshots',
      },
      {
        label: __('Scoped Search'),
        shouldShow: access.viewAdminPage && hasAttribute(AgencyAttributesEnums.enableSearchScoping),
        route: 'scoped-search',
      },
      {
        label: __('DataStore Settings'),
        shouldShow: access.canConfigureDataStore,
        route: 'datastore-settings',
      },
      {
        label: __('DataStore Secret Generation'),
        shouldShow:
          access.canAccessRecordsDataStoreModule || access.canAccessStandardsDataStoreModule,
        route: 'datastore-secret-generation',
      },
      {
        label: __('Inbox Manager'),
        shouldShow: shouldShowInboxManager,
        route: inboxManagerRoutes.BASE,
      },
    ],
    'label'
  )
}

function NavContainer({ NavItem, userContext }: NavContainerProps) {
  const history = useHistory()
  const { access } = useUserAccess([
    UserAccessEnum.CanAccessAuditLog,
    UserAccessEnum.CanAccessMasterChargeTable,
    UserAccessEnum.CanAccessMniDedupe,
    UserAccessEnum.CanEditMasterLocationTool,
    UserAccessEnum.ViewAdminPage,
    UserAccessEnum.ViewExpungements,
    UserAccessEnum.ViewFlagCategories,
    UserAccessEnum.ViewAcl,
    UserAccessEnum.CanAccessRecordsDataStoreModule,
    UserAccessEnum.CanAccessStandardsDataStoreModule,
    UserAccessEnum.CanConfigureDataStore,
  ])
  const { hasAttribute, isLiteAgency, agencyPartnerId, hasPartnerFeature } = userContext
  const { userAttributeSchema } = useQueryUserAttributes()
  const { getConfiguredString } = useConfiguredString()
  const districtLabelValue = getConfiguredString(AgencyAttributesEnums.districtWording)
  const districtFallbackTitle =
    userAttributeSchema?.schema?.district?.title || __('User Attributes')
  const grantLegacyFormBuilderAccess = useGrantLegacyFormBuilderAccess(
    hasAttribute(AgencyAttributesEnums.enableFormBuilder)
  )

  const navItems: NavItemConfig[] = getNavItems({
    access,
    hasAttribute,
    hasPartnerFeature,
    districtLabelValue,
    districtFallbackTitle,
    userAttributeSchema,
    history,
    grantLegacyFormBuilderAccess,
    isLiteAgency,
    agencyPartnerId,
  })

  return (
    <>
      {navItems
        .filter((navItem) => navItem.shouldShow)
        .map((navItem, index) => (
          <NavItem
            key={index} // eslint-disable-line react/no-array-index-key
            onClick={navItem.onClick}
            route={navItem.route}
          >
            {navItem.label}
          </NavItem>
        ))}
    </>
  )
}
