import * as React from 'react'
import { asyncRender, screen } from 'test/utils'
import { UnitAssignment, UnitToStateIdsMap } from 'pages/cases/types'
import { AssignUnitTreeNode } from './assignUnitTreeNode'

// Mock data for different test scenarios
const mockUnitToStateIdsMap: UnitToStateIdsMap = {
  'Detective Unit A': {
    activeStateId: 'state-1',
    triageStateId: 'state-2',
  },
  'Forensics Unit': {
    activeStateId: 'state-3',
    triageStateId: 'state-4',
  },
  'Special Investigations': {
    activeStateId: 'state-5',
    triageStateId: 'state-6',
  },
}

const mockPrimaryUnitAssignment: UnitAssignment = {
  unitId: 'unit-001',
  unitName: 'Detective Unit A',
  assignee: {
    id: 'det-001',
    badgeNumber: '1001',
    firstName: 'John',
    lastName: 'Smith',
    detectiveUnits: ['Detective Unit A'],
  },
  comment: 'Please prioritize this case.',
  dueAt: '2025-09-20',
  isUrgent: true,
}

const mockUnitWithoutAssignee: UnitAssignment = {
  unitId: 'unit-003',
  unitName: 'Special Investigations',
  dueAt: '2025-09-25',
  isUrgent: false,
}

const defaultProps = {
  onRemove: jest.fn(),
  unitToStateIdsMap: mockUnitToStateIdsMap,
  hideMarkAsUrgent: false,
  workflowIds: ['workflow-1'],
}

describe('AssignUnitTreeNode Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should display add detective button when no unit is provided', async () => {
    await asyncRender(<AssignUnitTreeNode {...defaultProps} />)

    expect(screen.getByRole('button', { name: /add detective/i })).toBeInTheDocument()
  })

  it('should display a tree node when it is open', async () => {
    await asyncRender(
      <AssignUnitTreeNode {...defaultProps} unit={mockPrimaryUnitAssignment} isUnitAssignment />
    )

    expect(screen.getByText('Detective Unit A')).toBeInTheDocument()
    expect(screen.getByLabelText('Due Date')).toHaveValue('09/20/2025')
    expect(screen.getByLabelText('Urgent')).toBeChecked()
    expect(screen.getByRole('button', { name: /Routing Comment/i })).toBeInTheDocument()
    expect(screen.getByTestId('comment-indicator')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /more actions/i })).toBeInTheDocument()
  })

  it('should display a tree node when it is open with secondary unit without comment', async () => {
    const unitWithoutComment = {
      ...mockPrimaryUnitAssignment,
      comment: undefined,
      dueAt: undefined,
      isUrgent: false,
    }

    await asyncRender(
      <AssignUnitTreeNode {...defaultProps} unit={unitWithoutComment} isUnitAssignment />
    )

    expect(screen.getByText('Detective Unit A')).toBeInTheDocument()
    expect(screen.getByLabelText('Due Date')).toHaveValue('')
    expect(screen.getByLabelText('Urgent')).not.toBeChecked()
    expect(screen.getByRole('button', { name: /Routing Comment/i })).toBeInTheDocument()
    expect(screen.queryByTestId('comment-indicator')).not.toBeInTheDocument()
  })

  it('should display assignee node', async () => {
    await asyncRender(
      <AssignUnitTreeNode
        {...defaultProps}
        unit={mockPrimaryUnitAssignment}
        isUnitAssignment={false}
      />
    )

    expect(screen.getByText('Smith, John (1001)')).toBeInTheDocument()
    expect(screen.queryByText('Due Date')).not.toBeInTheDocument()
    expect(screen.queryByText('Priority')).not.toBeInTheDocument()
    expect(screen.queryByRole('button', { name: /Routing Comment/i })).not.toBeInTheDocument()
    expect(screen.getByRole('button', { name: /more actions/i })).toBeInTheDocument()
  })

  it('should display a tree node when it is unit without assignee and do not have permission to view unit case', async () => {
    await asyncRender(
      <AssignUnitTreeNode {...defaultProps} unit={mockUnitWithoutAssignee} isUnitAssignment />
    )

    expect(screen.getByText('Special Investigations')).toBeInTheDocument()
    expect(screen.getByText('Due Date')).toBeInTheDocument()
    expect(screen.getByText('Priority')).toBeInTheDocument()
    expect(screen.getAllByText('—')).toHaveLength(2)
    expect(screen.queryByRole('button', { name: /Routing Comment/i })).not.toBeInTheDocument()
    expect(screen.queryByTestId('comment-indicator')).not.toBeInTheDocument()
  })

  it('should return unit suggest when in add new search mode and is unit assignment', async () => {
    await asyncRender(
      <AssignUnitTreeNode
        {...defaultProps}
        isAddNewSearch
        isUnitAssignment
        unit={mockPrimaryUnitAssignment}
      />
    )

    expect(screen.getByRole('textbox', { name: /unit/i })).toBeInTheDocument()
  })

  it('should return user suggest when in add new search mode', async () => {
    await asyncRender(
      <AssignUnitTreeNode {...defaultProps} isAddNewSearch unit={mockPrimaryUnitAssignment} />
    )

    expect(screen.getByLabelText(/assignee suggest/i)).toBeInTheDocument()
  })
})
