import * as React from 'react'
import { Avatar, SuggestionTypes, UserSuggest, getFieldErrorMessage } from 'iris-styleguide'
import { formatUserName } from '@axon-enterprise/formatters'
import {
  ActionMenu,
  Button,
  ButtonVariant,
  Caption,
  Checkbox,
  DateField,
  Grid,
  GridProps,
  Label,
  Stack,
  Subtitle,
} from '@axon-enterprise/spark'
import momentAdapter from '@axon-forked/rc-picker/lib/generate/moment'
import { CircleFilledIcon, IconColor, IconSize, MessageIcon } from '@axon-enterprise/icon'
import { UnitAssignment, UnitToStateIdsMap } from 'pages/cases/types'
import moment from 'moment/moment'
import { UnitSuggest } from 'pages/cases/components/unitSuggestion'
import { RmsModuleTypeEnum, User } from 'static/graphql/types'
import { first } from 'lodash'
import { mapActiveState } from 'pages/cases/mappers'

type Props = {
  onRemove: () => void
  isUnitAssignment?: boolean
  isPrimary?: boolean
  isAddNewSearch?: boolean
  isStandardsActive?: boolean
  hideMarkAsUrgent?: boolean
  unit?: UnitAssignment
  unitToStateIdsMap: UnitToStateIdsMap
  workflowIds?: string[]
} & GridProps

// eslint-disable-next-line complexity
export const AssignUnitTreeNode: React.FC<Props> = ({
  hideMarkAsUrgent = false,
  isUnitAssignment = false,
  isPrimary = false,
  isAddNewSearch = false,
  isStandardsActive = false,
  onRemove,
  unit,
  unitToStateIdsMap,
  workflowIds = [],
}) => {
  const [dueAtFieldFeedbackMessage, setDueAtFieldFeedbackMessage] = React.useState<string>()

  if (!unit) {
    return <Button variant={ButtonVariant.flatPrimary} text={__('ADD DETECTIVE')} />
  }
  const submodule = isStandardsActive ? RmsModuleTypeEnum.Standards : RmsModuleTypeEnum.Records

  // TODO: Add logic to check if user have permission to access unit's case
  const userCanViewUnitCase = !unit.unitName?.includes('Special') // Placeholder for actual permission logic

  if (isAddNewSearch) {
    const addUserPlaceHolder = unit.unitId
      ? __('Add $[unit] unit member by name or badge ID', {
          unit: unit.unitName,
        })
      : __('Search for a detective by name or badge ID')

    const workflowStateId = mapActiveState({ detectiveUnit: unit.unitName, unitToStateIdsMap })

    return isUnitAssignment ? (
      <UnitSuggest
        queryOnEmpty
        onChange={(_u) => {
          // Handle unit selection
        }}
        userId={unit.assignee?.id}
        unitToStateIdsMap={unitToStateIdsMap}
      />
    ) : (
      <UserSuggest
        aria-label={__('Assignee suggest')}
        margin="0"
        placeholder={addUserPlaceHolder}
        onChange={(_u) => {
          // Handle user selection
        }}
        type={SuggestionTypes.taskAssigneeWithCaseLoad}
        userToOptionConverter={(user: User) => {
          const { firstName = '', lastName = '', badgeNumber = '', caseCount = 0 } = user
          return {
            content: user,
            value: user.id || '',
            label: `${formatUserName(user)}  •  ${
              caseCount === 1
                ? __('1 active case')
                : __('$[count] active cases', { count: caseCount })
            }`,
            filterByLabel: `${firstName} ${lastName} ${badgeNumber}`,
          }
        }}
        value={unit.assignee}
        variables={{
          submodule,
          workflowId: first(workflowIds),
          workflowStateId,
        }}
        queryOnEmpty
      />
    )
  }

  return (
    <Grid columns={20} width="100%">
      <Grid.Item columnSpan={7} padding="s">
        <Stack alignItems="flex-start" justifyContent="center" direction="column" height="60px">
          {!isUnitAssignment && unit.assignee && (
            <Avatar
              marginLeft="0"
              size="auto"
              marginBottom="0"
              {...{ firstName: unit.assignee.firstName, lastname: unit.assignee.lastName }}
              label={formatUserName(unit.assignee)}
            />
          )}
          {isUnitAssignment && (
            <>
              <Subtitle as="h2" truncated>
                {unit.unitName}
              </Subtitle>
              {isPrimary && <Caption>{__('Primary Unit')}</Caption>}
            </>
          )}
        </Stack>
      </Grid.Item>
      <Grid.Item columnSpan={5} padding="s">
        <Stack alignItems="flex-start" justifyContent="center" direction="column" height="60px">
          {isUnitAssignment && !userCanViewUnitCase && (
            <>
              <Label as="h4">{__('Due Date')}</Label>
              <Caption as="h4">{__('—')}</Caption>
            </>
          )}
          {isUnitAssignment && userCanViewUnitCase && (
            <DateField
              dateAdapter={momentAdapter}
              label={__('Due Date')}
              value={unit.dueAt ? moment(unit.dueAt) : undefined}
              onChange={(_, value) => {
                const isError = moment(value).isBefore(moment(), 'day')
                const errorMessage = isError ? __('Due date cannot be in the past') : undefined
                setDueAtFieldFeedbackMessage(errorMessage)
                // Handle due date change
              }}
              error={getFieldErrorMessage(dueAtFieldFeedbackMessage)}
            />
          )}
        </Stack>
      </Grid.Item>
      <Grid.Item columnSpan={3} padding="s">
        <Stack alignItems="flex-start" justifyContent="center" direction="column" height="60px">
          {isUnitAssignment && !userCanViewUnitCase && !hideMarkAsUrgent && (
            <>
              <Label as="h4">{__('Priority')}</Label>
              <Caption as="h4">{__('—')}</Caption>
            </>
          )}
          {isUnitAssignment && userCanViewUnitCase && !hideMarkAsUrgent && (
            <>
              <Label>{__('Priority')}</Label>
              <Checkbox
                onChange={(_e) => {
                  // Handle urgent priority change
                }}
                label={__('Urgent')}
                checked={!!unit.isUrgent}
              />
            </>
          )}
        </Stack>
      </Grid.Item>
      <Grid.Item columnSpan={2} paddingStart="m">
        <Stack direction="row" alignItems="center" justifyContent="center" height="60px">
          {isUnitAssignment && userCanViewUnitCase && (
            <>
              <Button
                aria-label={__('Routing Comment')}
                tooltip={__('Add Routing Comment')}
                icon={MessageIcon}
                variant={ButtonVariant.flatPrimary}
                onClick={() => {}}
              />
              {!!unit.comment && (
                <CircleFilledIcon
                  size={IconSize.XXS}
                  color={IconColor.primary}
                  data-testid="comment-indicator"
                />
              )}
            </>
          )}
        </Stack>
      </Grid.Item>
      <Grid.Item columnSpan={3} padding="s">
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="flex-end"
          height="60px"
          width="100%"
        >
          <ActionMenu
            menuSize={ActionMenu.menuSizes.small}
            tooltip="More actions"
            variant={ActionMenu.variants.flatPrimary}
          >
            {unit && <ActionMenu.Item text={__('Unassign Unit')} onClick={() => {}} />}
            {unit && !isPrimary && (
              <ActionMenu.Item text={__('Assign as Primary Unit')} onClick={onRemove} />
            )}
            {!isUnitAssignment && (
              <ActionMenu.Item text={__('Unassign Detective')} onClick={onRemove} />
            )}
          </ActionMenu>
        </Stack>
      </Grid.Item>
    </Grid>
  )
}
