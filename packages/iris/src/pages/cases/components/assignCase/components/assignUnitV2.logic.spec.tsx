import { NodeId, TreeNodeObject } from '@axon-enterprise/spark'
import { CaseAssignmentV2, Detective, UnitAssignment } from 'pages/cases/types'
import {
  EmptyTreeNode,
  addNewEmptyNodeToCaseAssignmentTree,
  transformCaseAssignmentToTree,
} from './assignUnitV2.logic'

const createMockDetective = (overrides: Partial<Detective> = {}): Detective => ({
  id: 'detective-1',
  badgeNumber: '123',
  firstName: '<PERSON>',
  lastName: 'Doe',
  detectiveUnits: ['ARSON'],
  ...overrides,
})

const createMockUnitAssignment = (overrides: Partial<UnitAssignment> = {}): UnitAssignment => ({
  unitId: 'unit-1',
  unitName: 'Arson Unit',
  comment: 'Test comment',
  dueAt: '2023-12-01T10:00:00Z',
  isUrgent: false,
  ...overrides,
})

const createMockCaseAssignment = (units: UnitAssignment[]): CaseAssignmentV2 => ({
  caseId: 'case-1',
  units,
})

describe('transformCaseAssignmentToTree', () => {
  it('should create an empty tree with root node when no units are provided', () => {
    const assignment = createMockCaseAssignment([])
    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(2) // root node + child assignee
    expect(result.has('root' as NodeId)).toBe(true)
    expect(result.has('root::newAssignee' as NodeId)).toBe(true)

    const rootNode = result.get('root' as NodeId)
    expect(rootNode?.childIds).toEqual(['root::newAssignee' as NodeId])

    const assigneeNode = result.get('root::newAssignee' as NodeId)
    expect(assigneeNode?.parentId).toBe('root' as NodeId)
    expect(assigneeNode?.data).toEqual({})
  })

  it('should create tree nodes for units without assignees', () => {
    const unit1 = createMockUnitAssignment({ unitId: 'unit-1', unitName: 'Arson Unit' })
    const unit2 = createMockUnitAssignment({ unitId: 'unit-2', unitName: 'Homicide Unit' })
    const assignment = createMockCaseAssignment([unit1, unit2])

    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(4) // 2 units + 2 empty assignee nodes
    expect(result.has('0::unit-1' as NodeId)).toBe(true)
    expect(result.has('1::unit-2' as NodeId)).toBe(true)
    expect(result.has('0::unit-1::newAssignee' as NodeId)).toBe(true)
    expect(result.has('1::unit-2::newAssignee' as NodeId)).toBe(true)

    const unit1Node = result.get('0::unit-1' as NodeId)
    expect(unit1Node).toEqual({
      id: '0::unit-1' as NodeId,
      data: unit1,
      parentId: null,
      childIds: ['0::unit-1::newAssignee' as NodeId],
    })

    const unit2Node = result.get('1::unit-2' as NodeId)
    expect(unit2Node).toEqual({
      id: '1::unit-2' as NodeId,
      data: unit2,
      parentId: null,
      childIds: ['1::unit-2::newAssignee' as NodeId],
    })

    // Check the empty assignee nodes
    const unit1AssigneeNode = result.get('0::unit-1::newAssignee' as NodeId)
    expect(unit1AssigneeNode).toEqual({
      ...EmptyTreeNode,
      id: '0::unit-1::newAssignee' as NodeId,
      parentId: '0::unit-1' as NodeId,
      data: undefined,
    })

    const unit2AssigneeNode = result.get('1::unit-2::newAssignee' as NodeId)
    expect(unit2AssigneeNode).toEqual({
      ...EmptyTreeNode,
      id: '1::unit-2::newAssignee' as NodeId,
      parentId: '1::unit-2' as NodeId,
      data: undefined,
    })
  })

  it('should create tree nodes for units with assignees', () => {
    const detective = createMockDetective()
    const unit = createMockUnitAssignment({
      unitId: 'unit-1',
      unitName: 'Arson Unit',
      assignee: detective,
    })
    const assignment = createMockCaseAssignment([unit])

    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(2)
    expect(result.has('0::unit-1' as NodeId)).toBe(true)
    expect(result.has('0::unit-1::detective-1' as NodeId)).toBe(true)

    const unitNode = result.get('0::unit-1' as NodeId)
    expect(unitNode).toEqual({
      id: '0::unit-1' as NodeId,
      data: unit,
      parentId: null,
      childIds: ['0::unit-1::detective-1' as NodeId],
    })

    const assigneeNode = result.get('0::unit-1::detective-1' as NodeId)
    expect(assigneeNode).toEqual({
      id: '0::unit-1::detective-1' as NodeId,
      data: unit,
      parentId: '0::unit-1' as NodeId,
      childIds: [],
    })
  })

  it('should handle mixed units with and without assignees', () => {
    const detective = createMockDetective({ id: 'detective-1' })
    const unit1 = createMockUnitAssignment({
      unitId: 'unit-1',
      unitName: 'Arson Unit',
      assignee: detective,
    })
    const unit2 = createMockUnitAssignment({
      unitId: 'unit-2',
      unitName: 'Homicide Unit',
    })
    const unit3 = createMockUnitAssignment({
      unitId: 'unit-3',
      unitName: 'Robbery Unit',
      assignee: createMockDetective({ id: 'detective-2' }),
    })

    const assignment = createMockCaseAssignment([unit1, unit2, unit3])
    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(6) // 3 units + 3 child nodes (2 with assignees + 1 empty)

    expect(result.has('0::unit-1' as NodeId)).toBe(true)
    expect(result.has('1::unit-2' as NodeId)).toBe(true)
    expect(result.has('2::unit-3' as NodeId)).toBe(true)

    expect(result.has('0::unit-1::detective-1' as NodeId)).toBe(true)
    expect(result.has('1::unit-2::newAssignee' as NodeId)).toBe(true) // Empty assignee node
    expect(result.has('2::unit-3::detective-2' as NodeId)).toBe(true)

    const unit1Node = result.get('0::unit-1' as NodeId)
    expect(unit1Node?.childIds).toEqual(['0::unit-1::detective-1' as NodeId])

    const unit2Node = result.get('1::unit-2' as NodeId)
    expect(unit2Node?.childIds).toEqual(['1::unit-2::newAssignee' as NodeId])

    const unit3Node = result.get('2::unit-3' as NodeId)
    expect(unit3Node?.childIds).toEqual(['2::unit-3::detective-2' as NodeId])

    // Verify the empty assignee node
    const unit2AssigneeNode = result.get('1::unit-2::newAssignee' as NodeId)
    expect(unit2AssigneeNode).toEqual({
      ...EmptyTreeNode,
      id: '1::unit-2::newAssignee' as NodeId,
      parentId: '1::unit-2' as NodeId,
      data: undefined,
    })
  })

  it('should handle units with undefined or null assignees', () => {
    const unit1 = createMockUnitAssignment({
      unitId: 'unit-1',
      assignee: undefined,
    })
    const unit2 = createMockUnitAssignment({
      unitId: 'unit-2',
      assignee: null as any,
    })
    const assignment = createMockCaseAssignment([unit1, unit2])

    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(4) // 2 units + 2 empty assignee nodes
    expect(result.has('0::unit-1' as NodeId)).toBe(true)
    expect(result.has('1::unit-2' as NodeId)).toBe(true)
    expect(result.has('0::unit-1::newAssignee' as NodeId)).toBe(true)
    expect(result.has('1::unit-2::newAssignee' as NodeId)).toBe(true)

    const unit1Node = result.get('0::unit-1' as NodeId)
    const unit2Node = result.get('1::unit-2' as NodeId)

    expect(unit1Node?.childIds).toEqual(['0::unit-1::newAssignee' as NodeId])
    expect(unit2Node?.childIds).toEqual(['1::unit-2::newAssignee' as NodeId])

    // Verify the empty assignee nodes have undefined data
    const unit1AssigneeNode = result.get('0::unit-1::newAssignee' as NodeId)
    const unit2AssigneeNode = result.get('1::unit-2::newAssignee' as NodeId)
    expect(unit1AssigneeNode?.data).toBeUndefined()
    expect(unit2AssigneeNode?.data).toBeUndefined()
  })

  it('should handle empty case assignment with realistic tree structure', () => {
    const assignment = createMockCaseAssignment([])
    const result = transformCaseAssignmentToTree(assignment)

    expect(result.size).toBe(2)

    const rootNode = result.get('root' as NodeId)
    expect(rootNode).toEqual({
      ...EmptyTreeNode,
      id: 'root' as NodeId,
      childIds: ['root::newAssignee' as NodeId],
    })

    const assigneeNode = result.get('root::newAssignee' as NodeId)
    expect(assigneeNode).toEqual({
      ...EmptyTreeNode,
      id: 'root::newAssignee' as NodeId,
      parentId: 'root' as NodeId,
    })
  })

  it('should generate consistent node IDs based on unit order', () => {
    const unit1 = createMockUnitAssignment({ unitId: 'alpha', unitName: 'Alpha Unit' })
    const unit2 = createMockUnitAssignment({ unitId: 'beta', unitName: 'Beta Unit' })
    const assignment1 = createMockCaseAssignment([unit1, unit2])
    const assignment2 = createMockCaseAssignment([unit2, unit1]) // Different order

    const result1 = transformCaseAssignmentToTree(assignment1)
    const result2 = transformCaseAssignmentToTree(assignment2)

    // Should have different node IDs based on order
    expect(result1.has('0::alpha' as NodeId)).toBe(true)
    expect(result1.has('1::beta' as NodeId)).toBe(true)

    expect(result2.has('0::beta' as NodeId)).toBe(true)
    expect(result2.has('1::alpha' as NodeId)).toBe(true)
  })
})

describe('addNewEmptyNodeToCaseAssignmentTree', () => {
  let currentTree: Map<NodeId, TreeNodeObject<UnitAssignment | undefined>>

  beforeEach(() => {
    currentTree = new Map()
  })

  describe('adding new units', () => {
    it('should add unit without assignee to empty tree', () => {
      const result = addNewEmptyNodeToCaseAssignmentTree({
        currentTree,
        hasAssignee: false,
      })

      expect(result.size).toBe(2) // Unit + empty assignee child
      expect(result.has('0::newUnit' as NodeId)).toBe(true)
      expect(result.has('0::newUnit::newAssignee' as NodeId)).toBe(true)
      expect(currentTree.size).toBe(0)

      const newUnitNode = result.get('0::newUnit' as NodeId)
      expect(newUnitNode).toEqual({
        ...EmptyTreeNode,
        id: '0::newUnit' as NodeId,
        childIds: ['0::newUnit::newAssignee' as NodeId],
      })

      const assigneeNode = result.get('0::newUnit::newAssignee' as NodeId)
      expect(assigneeNode).toEqual({
        ...EmptyTreeNode,
        id: '0::newUnit::newAssignee' as NodeId,
        parentId: '0::newUnit' as NodeId,
        data: undefined,
      })
    })

    it('should add unit with assignee to empty tree', () => {
      const result = addNewEmptyNodeToCaseAssignmentTree({
        currentTree,
        hasAssignee: true,
      })

      expect(result.size).toBe(2)
      expect(result.has('0::newUnit' as NodeId)).toBe(true)
      expect(result.has('0::newUnit::newAssignee' as NodeId)).toBe(true)
      expect(currentTree.size).toBe(0)

      const unitNode = result.get('0::newUnit' as NodeId)
      expect(unitNode).toEqual({
        ...EmptyTreeNode,
        id: '0::newUnit' as NodeId,
        childIds: ['0::newUnit::newAssignee' as NodeId],
      })

      const assigneeNode = result.get('0::newUnit::newAssignee' as NodeId)
      expect(assigneeNode).toEqual({
        ...EmptyTreeNode,
        id: '0::newUnit::newAssignee' as NodeId,
        parentId: '0::newUnit' as NodeId,
      })
    })

    it('should add unit to existing tree with correct index calculation', () => {
      const existingUnit = createMockUnitAssignment()
      currentTree.set('0::existing-unit' as NodeId, {
        id: '0::existing-unit' as NodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })
      currentTree.set('1::another-unit' as NodeId, {
        id: '1::another-unit' as NodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })

      const result = addNewEmptyNodeToCaseAssignmentTree({
        currentTree,
        hasAssignee: false,
      })

      expect(result.size).toBe(4) // 2 existing + 1 new unit + 1 new assignee
      expect(result.has('2::newUnit' as NodeId)).toBe(true)
      expect(result.has('2::newUnit::newAssignee' as NodeId)).toBe(true)
      expect(result.has('0::existing-unit' as NodeId)).toBe(true)
      expect(result.has('1::another-unit' as NodeId)).toBe(true)
      expect(currentTree.size).toBe(2)

      const newNode = result.get('2::newUnit' as NodeId)
      expect(newNode).toEqual({
        ...EmptyTreeNode,
        id: '2::newUnit' as NodeId,
        childIds: ['2::newUnit::newAssignee' as NodeId],
      })

      const newAssigneeNode = result.get('2::newUnit::newAssignee' as NodeId)
      expect(newAssigneeNode).toEqual({
        ...EmptyTreeNode,
        id: '2::newUnit::newAssignee' as NodeId,
        parentId: '2::newUnit' as NodeId,
        data: undefined,
      })

      const existingNode1 = result.get('0::existing-unit' as NodeId)
      const existingNode2 = result.get('1::another-unit' as NodeId)
      expect(existingNode1?.data).toEqual(existingUnit)
      expect(existingNode2?.data).toEqual(existingUnit)
    })
  })

  describe('adding new assignees', () => {
    it('should add assignee to existing unit with proper node ID calculation', () => {
      const parentNodeId = '0::existing-unit' as NodeId
      const existingUnit = createMockUnitAssignment()

      currentTree.set(parentNodeId, {
        id: parentNodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })
      currentTree.set('1::another-unit' as NodeId, {
        id: '1::another-unit' as NodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })
      currentTree.set('2::third-unit' as NodeId, {
        id: '2::third-unit' as NodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })

      const result = addNewEmptyNodeToCaseAssignmentTree({
        currentTree,
        parentNodeId,
      })

      expect(result.size).toBe(4)
      expect(result.has('3::0::existing-unit' as NodeId)).toBe(true)
      expect(result.has(parentNodeId)).toBe(true)
      expect(result.has('1::another-unit' as NodeId)).toBe(true)
      expect(result.has('2::third-unit' as NodeId)).toBe(true)
      expect(currentTree.size).toBe(3)

      const assigneeNode = result.get('3::0::existing-unit' as NodeId)
      expect(assigneeNode).toEqual({
        ...EmptyTreeNode,
        id: '3::0::existing-unit' as NodeId,
        parentId: parentNodeId,
      })

      const existingParentNode = result.get(parentNodeId)
      expect(existingParentNode?.id).toBe(parentNodeId)
      expect(existingParentNode?.data).toEqual(existingUnit)
    })

    it('should ignore hasAssignee parameter when parentNodeId is provided', () => {
      const parentNodeId = '0::existing-unit' as NodeId
      const existingUnit = createMockUnitAssignment()

      currentTree.set(parentNodeId, {
        id: parentNodeId,
        data: existingUnit,
        parentId: null,
        childIds: [],
      })

      const result = addNewEmptyNodeToCaseAssignmentTree({
        currentTree,
        parentNodeId,
        hasAssignee: true,
      })

      expect(result.size).toBe(2)
      expect(result.has('1::0::existing-unit' as NodeId)).toBe(true)
      expect(result.has('1::0::existing-unit::newAssignee' as NodeId)).toBe(false)
      expect(currentTree.size).toBe(1)

      const assigneeNode = result.get('1::0::existing-unit' as NodeId)
      expect(assigneeNode).toEqual({
        ...EmptyTreeNode,
        id: '1::0::existing-unit' as NodeId,
        parentId: parentNodeId,
      })
    })
  })
})
