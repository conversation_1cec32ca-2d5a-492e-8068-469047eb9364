import { CaseAssignmentV2, UnitAssignment } from 'pages/cases/types'
import { NodeId, TreeNodeObject } from '@axon-enterprise/spark'

export const EmptyTreeNode = {
  id: 'root' as NodeId,
  data: {},
  parentId: null,
  childIds: [],
}

export function transformCaseAssignmentToTree(
  assignment: CaseAssignmentV2
): Map<NodeId, TreeNodeObject<UnitAssignment | undefined>> {
  const tree = new Map<NodeId, TreeNodeObject<UnitAssignment | undefined>>()
  assignment.units.forEach((unit, index) => {
    const nodeId = `${index}::${unit.unitId}` as NodeId
    // eslint-disable-next-line no-restricted-syntax
    let childNodeId: NodeId
    if (unit.assignee) {
      childNodeId = `${nodeId}::${unit.assignee.id}` as NodeId
      tree.set(childNodeId, {
        id: childNodeId,
        data: unit,
        parentId: nodeId,
        childIds: [],
      })
    } else {
      childNodeId = `${nodeId}::newAssignee` as NodeId
      // Only add empty node if there is no assignee
      tree.set(childNodeId, {
        ...EmptyTreeNode,
        id: childNodeId,
        parentId: nodeId,
        data: undefined,
      })
    }
    tree.set(nodeId, {
      id: nodeId,
      data: unit,
      parentId: null,
      childIds: [childNodeId],
    })
  })
  if (tree.size === 0) {
    const rootNodeId = 'root' as NodeId
    const childNodeId = `${rootNodeId}::newAssignee` as NodeId
    tree.set(childNodeId, {
      ...EmptyTreeNode,
      id: childNodeId,
      parentId: rootNodeId,
    })
    tree.set(rootNodeId, {
      ...EmptyTreeNode,
      id: rootNodeId,
      childIds: [childNodeId],
    })
  }
  return tree
}

export function addNewEmptyNodeToCaseAssignmentTree({
  currentTree,
  parentNodeId,
  hasAssignee = false,
}: {
  currentTree: Map<NodeId, TreeNodeObject<UnitAssignment | undefined>>
  parentNodeId?: NodeId
  hasAssignee?: boolean
}): Map<NodeId, TreeNodeObject<UnitAssignment | undefined>> {
  // We assume that if there is parentNodeId, we are adding a new assignee to an existing unit
  const newTree = new Map(currentTree)
  if (parentNodeId) {
    const nodeID = `${newTree.size}::${parentNodeId}` as NodeId
    newTree.set(nodeID, {
      ...EmptyTreeNode,
      id: nodeID,
      parentId: parentNodeId,
    })
  } else {
    // If there is no parentNodeId, we are adding a new unit
    const nodeID = `${newTree.size}::newUnit` as NodeId
    const childNodeId = `${nodeID}::newAssignee` as NodeId
    if (hasAssignee) {
      newTree.set(childNodeId, {
        ...EmptyTreeNode,
        id: childNodeId,
        parentId: nodeID,
      })
    } else {
      newTree.set(childNodeId, {
        ...EmptyTreeNode,
        id: childNodeId,
        parentId: nodeID,
        data: undefined,
      })
    }
    newTree.set(nodeID, {
      ...EmptyTreeNode,
      id: nodeID,
      parentId: null,
      childIds: [childNodeId],
    })
  }
  return newTree
}
