import * as React from 'react'
import { asyncRender, screen } from 'test/utils'
import { CaseAssignmentV2, UnitToStateIdsMap } from 'pages/cases/types'
import { WorkflowTypeEnum } from 'static/graphql/types'
import moment from 'moment'
import { AssignUnitV2Form } from './assignUnitV2'

const mockUnitToStateIdsMap: UnitToStateIdsMap = {
  'Detective Unit A': {
    activeStateId: 'state-1',
    triageStateId: 'state-2',
  },
  'Forensics Unit': {
    activeStateId: 'state-3',
    triageStateId: 'state-4',
  },
  'Special Investigations': {
    activeStateId: 'state-5',
    triageStateId: 'state-6',
  },
}

const mockCaseAssignmentWithMultipleRows: CaseAssignmentV2 = {
  caseId: 'case-123',
  units: [
    {
      unitId: 'unit-001',
      unitName: 'Detective Unit A',
      assignee: {
        id: 'det-001',
        badgeNumber: '1001',
        firstName: 'John',
        lastName: 'Smith',
        detectiveUnits: ['Detective Unit A'],
      },
      dueAt: moment().add(3, 'days').format('YYYY-MM-DD'),
      isUrgent: false,
    },
    {
      unitId: 'unit-002',
      unitName: 'Forensics Unit',
      assignee: {
        id: 'det-002',
        badgeNumber: '1002',
        firstName: 'Sarah',
        lastName: 'Johnson',
        detectiveUnits: ['Forensics Unit'],
      },
      comment: 'Evidence analysis required',
      dueAt: moment().add(5, 'days').format('YYYY-MM-DD'),
      isUrgent: true,
    },
    {
      unitId: 'unit-003',
      unitName: 'Special Investigations',
      dueAt: moment().add(7, 'days').format('YYYY-MM-DD'),
      isUrgent: false,
    },
  ],
}

const defaultProps = {
  caseWorkflowType: WorkflowTypeEnum.Case as const,
  onUpdateAssignment: jest.fn(),
  onFormError: jest.fn(),
  unitToStateIdsMap: mockUnitToStateIdsMap,
  unitLabel: 'Unit',
  assigneeLabel: 'Detective',
}

describe('AssignUnitV2Form component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should display a form when it is open', async () => {
    await asyncRender(<AssignUnitV2Form {...defaultProps} />)

    expect(screen.getByText('ADD UNIT')).toBeInTheDocument()
    expect(screen.getByText('SEARCH FOR A SPECIFIC DETECTIVE')).toBeInTheDocument()
  })

  it('should display a form when it is open with multiple units', async () => {
    await asyncRender(
      <AssignUnitV2Form {...defaultProps} assignment={mockCaseAssignmentWithMultipleRows} />
    )

    expect(screen.getByText('ADD UNIT')).toBeInTheDocument()
    expect(screen.getByText('SEARCH FOR A SPECIFIC DETECTIVE')).toBeInTheDocument()

    expect(screen.getByText('Detective Unit A')).toBeInTheDocument()
    expect(screen.getByText('Primary Unit')).toBeInTheDocument()
    expect(screen.getByText('Smith, John (1001)')).toBeInTheDocument()

    expect(screen.getByText('Forensics Unit')).toBeInTheDocument()
    expect(screen.getByText('Johnson, Sarah (1002)')).toBeInTheDocument()
    expect(screen.getByTestId('comment-indicator')).toBeInTheDocument()

    expect(screen.getByText('Special Investigations')).toBeInTheDocument()
    expect(screen.getAllByText('—')).toHaveLength(2)

    const urgentCheckboxes = screen.getAllByLabelText('Urgent')
    expect(urgentCheckboxes).toHaveLength(2)
    expect(urgentCheckboxes[0]).not.toBeChecked()
    expect(urgentCheckboxes[1]).toBeChecked()

    expect(screen.getAllByLabelText('Due Date')).toHaveLength(2)
  })
})
