import * as React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>V<PERSON>t,
  Di<PERSON><PERSON>,
  NodeId,
  <PERSON>ack,
  <PERSON>,
  TreeNodeObject,
} from '@axon-enterprise/spark'
import { WorkflowTypeEnum } from 'static/graphql/types'
import { CaseAssignmentV2, UnitAssignment, UnitToStateIdsMap } from 'pages/cases/types'
import moment from 'moment'
import { AssignUnitTreeNode } from './assignUnitTreeNode'
import { transformCaseAssignmentToTree } from './assignUnitV2.logic'

type Props = {
  assignment?: CaseAssignmentV2
  caseWorkflowType: WorkflowTypeEnum.Case | WorkflowTypeEnum.StandardsCase
  onUpdateAssignment: (updatedAssignment: CaseAssignmentV2) => void
  onFormError: (isError: boolean, errorMessage?: string[]) => void
  unitToStateIdsMap: UnitToStateIdsMap
  unitLabel?: string
  assigneeLabel?: string
}

// eslint-disable-next-line complexity
export const AssignUnitV2Form: React.FC<Props> = ({ assignment, unitToStateIdsMap }) => {
  // Generate mock assignment data for testing
  const mockAssignment: CaseAssignmentV2 = {
    caseId: 'case-123',
    units: [
      {
        unitId: 'unit-001',
        unitName: 'Detective Unit A',
        assignee: {
          id: 'det-001',
          badgeNumber: '1001',
          firstName: 'John',
          lastName: 'Smith',
          detectiveUnits: ['Detective Unit A'],
        },
        dueAt: moment().add(3, 'days').format('YYYY-MM-DD'),
        isUrgent: false,
      },
      {
        unitId: 'unit-002',
        unitName: 'Forensics Unit',
        assignee: {
          id: 'det-002',
          badgeNumber: '1002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          detectiveUnits: ['Forensics Unit'],
        },
        comment: 'Evidence analysis required',
        dueAt: moment().add(5, 'days').format('YYYY-MM-DD'),
        isUrgent: true,
      },
      {
        unitId: 'unit-003',
        unitName: 'Special Investigations',
        // No assignee for this unit
        dueAt: moment().add(7, 'days').format('YYYY-MM-DD'),
        isUrgent: false,
      },
    ],
  }

  const treeData: Map<
    NodeId,
    TreeNodeObject<UnitAssignment | undefined>
  > = transformCaseAssignmentToTree(assignment || mockAssignment)

  return (
    <Stack direction="column" width="100%">
      <Tree
        data={treeData}
        variant="card"
        renderNode={(node) => (
          <Tree.Node id={node.id}>
            <AssignUnitTreeNode
              unit={node.data}
              isUnitAssignment={!node.parentId}
              isPrimary={node.id.toString().includes('unit-001')}
              onRemove={() => {
                // Handle removal - implement actual removal logic
              }}
              hideMarkAsUrgent={false}
              unitToStateIdsMap={unitToStateIdsMap}
            />
          </Tree.Node>
        )}
      />

      <Stack direction="row" space="m" paddingTop="l">
        <Button variant={ButtonVariant.flatPrimary} text={__('ADD UNIT')} />
        <Divider vertical />
        <Button variant={ButtonVariant.flatPrimary} text={__('SEARCH FOR A SPECIFIC DETECTIVE')} />
      </Stack>
    </Stack>
  )
}
