import * as React from 'react'
import { WorkflowTypeEnum } from 'static/graphql/types'
import {
  asyncRender,
  fireEvent,
  rtlChangeAutocomplete,
  rtlUpdateAutocomplete,
  screen,
  waitFor,
} from 'test/utils'
import { mockCase } from 'pages/cases/mocks'
import { Detective } from 'pages/cases/types'
import { getUnitToStateIdsMapFromHardcodedInboxes } from 'shared/hooks'
import { mapTriageState } from 'pages/cases/mappers'
import { DetectiveUnit } from 'pages/cases/enums'
import { WorkflowStateTypes } from 'shared/task'
import { getUser } from 'test/mocks/user'
import { formatUserName } from '@axon-enterprise/formatters'

import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { mockUseUserContext } from 'test/mocks/userContext'
import { AssignUnitBaseDrawer } from './assignUnitBaseDrawer'

// Mock the useGetInvestigativeUnits hook to prevent interference with filtering logic
jest.mock('pages/adminConsole/queries/investigativeUnit/useGetInvestigativeUnits', () => ({
  useGetInvestigativeUnits: jest.fn(() => ({
    data: undefined,
    error: undefined,
    loading: false,
    refetch: jest.fn(),
  })),
}))

describe('AssignUnitBaseDrawer component', () => {
  const {
    dueAt,
    detectiveUnits,
    incident,
    owner,
    task,
    workflowStateId = '',
    workflowStateType = '',
  } = mockCase

  const defaultProps: React.ComponentProps<typeof AssignUnitBaseDrawer> = {
    caseAssignment: {
      assignee: owner as Detective,
      dueAt,
      isUrgent: false,
      unit: detectiveUnits?.[0],
    },
    caseTask: task,
    caseWorkflowStateId: workflowStateId,
    caseWorkflowStateType: workflowStateType,
    caseWorkflowType: WorkflowTypeEnum.Case,
    caseWorkflowShouldFastForward: false,
    isOpen: false,
    onCaseUpdate: jest.fn(),
    onClose: jest.fn(),
    parentFriendlyId: incident.friendlyId,
  }

  it('should display nothing when it is closed', async () => {
    await asyncRender(<AssignUnitBaseDrawer {...defaultProps} />)

    expect(screen.queryAllByRole('button').length).toBe(0)
  })

  it('should display a drawer when it is open', async () => {
    await asyncRender(<AssignUnitBaseDrawer {...defaultProps} isOpen />)

    await screen.getByText(`Transfer Unit - ${mockCase.incident.friendlyId}`)
    screen.getByLabelText(/routing note/i)
    screen.getByLabelText(/mark as urgent/i)
  })

  it('should display drawer v2 when enableMultiUnitCase is true', async () => {
    mockUseUserContext({
      overrides: {
        hasAttribute: (att) => att === AgencyAttributesEnums.enableMultiUnitCase,
      },
    })

    await asyncRender(<AssignUnitBaseDrawer {...defaultProps} isOpen />)

    screen.getByText(`Transfer Unit - ${mockCase.incident.friendlyId}`)
    screen.getByRole('button', { name: /add unit/i })
    screen.getByRole('button', { name: /search for a specific detective/i })
  })

  it.each([false, true])(
    'should transfer unit via updateTaskInbox which updates task with caseWorkflowShouldFastForward %s',
    async (caseWorkflowShouldFastForward) => {
      const mutationSpy = jest.fn().mockImplementation(() => Promise.resolve({}))
      const props = {
        ...defaultProps,
        caseWorkflowShouldFastForward,
      }
      await asyncRender(<AssignUnitBaseDrawer {...props} isOpen />, {
        mutateFn: mutationSpy,
      })

      fireEvent.click(screen.getByRole('button', { name: /transfer/i }))

      await waitFor(() => expect(mutationSpy).toHaveBeenCalled())
      expect(mutationSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          variables: {
            comment: '',
            currentTask: {
              lastAssignedAt: task.lastAssignedAt,
              taskId: task.id,
            },
            dueAt,
            dueDate: { value: dueAt },
            generateAllGraphTransitionEvents: caseWorkflowShouldFastForward ? false : undefined,
            id: task.id,
            nextPriority: 'MEDIUM',
            nextWorkflowStateId: mapTriageState({
              detectiveUnit: detectiveUnits?.[0],
              unitToStateIdsMap: getUnitToStateIdsMapFromHardcodedInboxes(),
            }),
          },
        })
      )
    }
  )

  it.each([false, true])(
    'should transfer unit via updateTaskAssignment which updates task with caseWorkflowShouldFastForward %s',
    async (caseWorkflowShouldFastForward) => {
      const mutationSpy = jest.fn().mockImplementation(() => Promise.resolve({}))
      const props = {
        ...defaultProps,
        caseWorkflowShouldFastForward,
      }
      await asyncRender(<AssignUnitBaseDrawer {...props} isOpen />, {
        mutateFn: mutationSpy,
      })

      // Unit change triggers assignee removal.
      const nextUnit = DetectiveUnit.ARSON
      const input = await screen.findByLabelText(/^unit$/i)
      rtlUpdateAutocomplete(input, nextUnit)

      fireEvent.click(screen.getByRole('button', { name: /transfer/i }))

      await waitFor(() => expect(mutationSpy).toHaveBeenCalled())
      expect(mutationSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          variables: {
            comment: '',
            currentTask: {
              lastAssignedAt: task.lastAssignedAt,
              taskId: task.id,
            },
            dueAt,
            dueDate: { value: dueAt },
            generateAllGraphTransitionEvents: caseWorkflowShouldFastForward ? false : undefined,
            id: task.id,
            nextOwnerId: undefined,
            nextPriority: 'MEDIUM',
            nextWorkflowStateId: mapTriageState({
              detectiveUnit: nextUnit,
              unitToStateIdsMap: getUnitToStateIdsMapFromHardcodedInboxes(),
            }),
          },
        })
      )
    }
  )

  it.each([
    [
      'should call reassign draft on case summary if case summary is in draft and author has changed',
      true,
      WorkflowStateTypes.DRAFT,
    ],
    [
      'should not call reassign draft on a case summary if case summary is finalized',
      false,
      WorkflowStateTypes.INDELIBLE,
    ],
  ])(
    'should reassign case summary if it is in draft state and author has changed, not if it is finalized ',
    async (_, shouldCallReassignDraft, workflowStateTypeForTest) => {
      const mutationSpy = jest.fn().mockImplementation(() => Promise.resolve({}))
      const mockUser = getUser({ seed: 1337 })

      const propsWithCaseSummary = {
        caseSummary: {
          id: 'caseSummaryId',
          tasks: {
            results: [
              {
                id: 'caseSummaryTaskId',
                workflowState: { type: workflowStateTypeForTest },
              },
            ],
          },
        },
        ...defaultProps,
      }
      await asyncRender(<AssignUnitBaseDrawer {...propsWithCaseSummary} isOpen />, {
        mocks: {
          Users: () => ({ results: [mockUser, getUser()] }),
        },
        mutateFn: mutationSpy,
      })

      // Unit change triggers assignee removal.
      const nextUnit = DetectiveUnit.ARSON
      const unitInput = await screen.findByLabelText(/^unit$/i)
      rtlUpdateAutocomplete(unitInput, nextUnit)

      await rtlChangeAutocomplete(screen.getByLabelText(/assignee suggest/i), 'a')
      fireEvent.click(screen.getByText(formatUserName(mockUser), { exact: false }))
      fireEvent.click(screen.getByRole('button', { name: /transfer/i }))

      if (shouldCallReassignDraft) {
        await waitFor(() => expect(mutationSpy).toHaveBeenCalledTimes(2))

        expect(mutationSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            variables: {
              documentId: 'caseSummaryId',
              newAuthorId: mockUser.id,
              nextPriority: 'MEDIUM',
            },
          })
        )
      } else {
        await waitFor(() => expect(mutationSpy).toHaveBeenCalledTimes(1))
      }
    }
  )
})
