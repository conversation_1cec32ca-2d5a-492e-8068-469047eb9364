import * as React from 'react'
import { log } from 'shared/log'
import { first } from 'lodash'
import { Error<PERSON>ialog, NextButton, useReassignDraftMutation } from 'iris-styleguide'
import { Drawer } from '@axon-enterprise/spark'
import { WorkflowStateTypes, useUpdateTaskMutation } from 'shared/task'
import { MixpanelUpdateTaskViews, WorkflowStateType } from 'pages/cases/enums'
import { CaseAssignment, Task } from 'pages/cases/types'
import { usePermittedCaseInboxes } from 'shared/hooks'
import {
  Document,
  Documents,
  EntityEnum,
  TaskPriorityEnum,
  WorkflowTypeEnum,
} from 'static/graphql/types'
import { useUserContext } from 'store/context'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { AssignUnitV2Form } from 'pages/cases/components/assignCase/components/assignUnitV2'
import {
  AssignDetectiveForm,
  CaseActions,
  getCaseTaskDocumentsToReassign,
  hasDetectiveChanged,
  hasUnitChanged,
  updateCaseTaskDocumentAssignment,
  updateTaskAssignment,
  updateTaskInbox,
} from '../assignCase'

type Props = {
  caseAssignment: CaseAssignment
  caseTask: Task
  caseTaskDocuments?: Documents
  caseSummary?: Document
  caseWorkflowStateId: string
  caseWorkflowStateType: string
  caseWorkflowShouldFastForward: boolean
  caseWorkflowType: WorkflowTypeEnum.Case | WorkflowTypeEnum.StandardsCase
  isOpen: boolean
  onCaseUpdate?: () => void
  onClose: () => void
  parentFriendlyId: string
}

const initialState = {
  isUrgent: false,
}

export const AssignUnitBaseDrawer: React.FC<Props> = ({
  caseAssignment,
  caseTask,
  caseSummary,
  caseTaskDocuments,
  caseWorkflowStateId,
  caseWorkflowStateType,
  caseWorkflowShouldFastForward,
  caseWorkflowType,
  isOpen,
  onCaseUpdate = () => {},
  onClose,
  parentFriendlyId,
}) => {
  const { hasAttribute } = useUserContext()
  const hasEnableMultiUnitCase = hasAttribute(AgencyAttributesEnums.enableMultiUnitCase)

  const [updateTaskMutation, { loading }] = useUpdateTaskMutation()
  const [reassignDraftMutation] = useReassignDraftMutation()
  const { unitToStateIdsMap } = usePermittedCaseInboxes({
    caseWorkflowType,
  })
  const {
    assignee: defaultDetective,
    dueAt,
    isUrgent,
    unit: defaultUnit,
    title: defaultTitle,
  } = caseAssignment

  const [assignment, setAssignment] = React.useState<CaseAssignment>(initialState)
  const [error, setError] = React.useState('')
  const [formIsInvalid, setFormIsInvalid] = React.useState(false)

  React.useEffect(() => {
    setAssignment({
      assignee: defaultDetective,
      dueAt,
      isUrgent,
      unit: defaultUnit,
      title: defaultTitle,
    })
  }, [defaultDetective, defaultUnit, defaultTitle, dueAt, isOpen, isUrgent])

  // eslint-disable-next-line complexity, max-statements
  const onSave = async ({ closeDrawer }: { closeDrawer: () => void }) => {
    try {
      if (!caseTask) {
        throw new Error(
          `${__('Update unsuccessful. Task was not found for the case')} ${parentFriendlyId}`
        )
      }

      if (!assignment.unit) {
        throw new Error(`${__('Could not find detective unit')}`)
      }

      const caseTaskDocumentsGqlTasksToReassign = getCaseTaskDocumentsToReassign(
        caseTaskDocuments,
        defaultDetective
      )
      const additionalTasksToReassign: Task[] = caseTaskDocumentsGqlTasksToReassign.map(
        (gqlTask) =>
          ({
            id: gqlTask.id,
            lastAssignedAt: gqlTask.lastAssignedAt,
            actions: gqlTask.actions,
            assignmentMeta: gqlTask.assignmentMeta,
            owner: gqlTask.owner,
          } as unknown as Task)
      )

      if (
        hasDetectiveChanged({
          assignmentDetective: assignment.assignee,
          caseDetective: defaultDetective,
        })
      ) {
        await Promise.all([
          updateTaskAssignment({
            task: caseTask,
            assignment,
            detectiveUnit: assignment.unit,
            fastForward: caseWorkflowShouldFastForward,
            unitToStateIdsMap,
            updateTaskMutation,
            entityType: EntityEnum.Case,
            initiatedFrom: MixpanelUpdateTaskViews.TransferUnit,
          }),
          ...additionalTasksToReassign.map((caseTaskRQTask) =>
            updateCaseTaskDocumentAssignment({
              task: caseTaskRQTask,
              assignment,
              detectiveUnit: assignment.unit || '',
              unitToStateIdsMap,
              updateTaskMutation,
            })
          ),
        ])
        const newAuthorId = assignment.assignee?.id
        if (caseSummary && newAuthorId) {
          const caseSummaryTask = first(caseSummary.tasks?.results)
          const currentPriority =
            first(caseSummary.tasks?.results)?.priority || TaskPriorityEnum.Medium

          if (caseSummaryTask?.workflowState?.type === WorkflowStateTypes.DRAFT) {
            reassignDraftMutation({
              variables: {
                documentId: caseSummary.id || '',
                newAuthorId,
                nextPriority: currentPriority,
              },
            })
          }
        }
      } else {
        await updateTaskInbox({
          task: caseTask,
          assignment,
          detectiveUnit: assignment.unit,
          fastForward: caseWorkflowShouldFastForward,
          unitToStateIdsMap,
          updateTaskMutation,
        })
      }
    } catch (e) {
      const errorMessage = e instanceof Error ? e.toString() : `${e.code}: ${e.reasons?.[0] || ''}`
      setError(errorMessage)
      log.error(errorMessage)

      return
    }

    onCaseUpdate()
    closeDrawer()
  }

  const isDisabled = () => {
    const isDetectiveRequired = caseWorkflowStateType === WorkflowStateType.ACTIVE
    const detectiveChanged =
      assignment.assignee &&
      hasDetectiveChanged({
        assignmentDetective: assignment.assignee,
        caseDetective: defaultDetective,
      })
    const unitChanged = hasUnitChanged({
      detectiveUnit: assignment.unit,
      unitToStateIdsMap,
      workflowStateId: caseWorkflowStateId || '',
    })

    return !unitChanged || (isDetectiveRequired && !detectiveChanged) || formIsInvalid
  }

  return (
    <Drawer
      title={`${__('Transfer Unit - ')}${parentFriendlyId}`}
      open={isOpen}
      onClose={onClose}
      primaryAction={
        <NextButton
          loading={loading}
          disabled={isDisabled()}
          onClick={() => onSave({ closeDrawer: onClose })}
          text={__('Transfer')}
        />
      }
      large
    >
      {hasEnableMultiUnitCase && (
        <AssignUnitV2Form
          caseWorkflowType={caseWorkflowType}
          onUpdateAssignment={() => {}}
          onFormError={(isError) => setFormIsInvalid(isError)}
          unitToStateIdsMap={unitToStateIdsMap}
        />
      )}
      {!hasEnableMultiUnitCase && (
        <AssignDetectiveForm
          caseWorkflowType={caseWorkflowType}
          detective={defaultDetective}
          action={CaseActions.TRANSFER_UNIT}
          assignment={assignment}
          onUpdateAssignment={(updatedAssignment) => setAssignment(updatedAssignment)}
          onFormError={(isError) => setFormIsInvalid(isError)}
          unitToStateIdsMap={unitToStateIdsMap}
        />
      )}
      <ErrorDialog
        onClose={() => {
          setError('')
          onClose()
        }}
        open={!!error}
        primaryButtonText={__('OK')}
        title={__('Error')}
      >
        {error}
      </ErrorDialog>
    </Drawer>
  )
}
