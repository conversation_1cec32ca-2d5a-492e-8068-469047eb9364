/* eslint-disable max-lines */
import { Body1, <PERSON>ack, TextColor, Title } from '@axon-enterprise/spark'
import { Body2, Grid, Label, NextAutocomplete, NextButton, NextRadioGroup } from 'iris-styleguide'
import { isUndefined, last, omitBy, startCase } from 'lodash'
import { WorkflowStateType, WorkflowStateTypeLabel } from 'pages/cases/enums'
import { getLastOwner } from 'pages/cases/mappers'
import { Detective, Task } from 'pages/cases/types'
import * as React from 'react'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import {
  CaseDispositionOptionsType,
  DEFAULT_UCR_DISPOSITION,
  IncidentDispositionOptionsType,
  getCaseEnumOptions,
  getIncidentEnumOptions,
  shouldShowClearanceData,
} from 'shared/disposition'
import { usePermittedCaseInboxes, useUserAccess } from 'shared/hooks'
import { Incident, UserAccessEnum, WorkflowTypeEnum } from 'static/graphql/types'
import { useUserContext } from 'store/context'
import { AssignDetectiveForm, CaseActions } from '../assignCase'
import { ExceptionalClearanceForm } from './components'
import {
  DispositionFormErrorsType,
  DispositionStateType,
  mapDispositionToWorkflowState,
} from './utils'
import {
  ReportValidationFeedback,
  ReportValidationFeedbackProps,
} from './components/reportValidationFeedback'

type Props = {
  caseDispositionOptions: CaseDispositionOptionsType | undefined
  caseTask: Task
  caseWorkflowType: WorkflowTypeEnum.Case | WorkflowTypeEnum.StandardsCase
  defaultDisposition?: string
  detectiveUnits: Array<string>
  incidentDispositionOptions?: IncidentDispositionOptionsType | undefined
  isOpen: boolean
  caseInboxRequiresReview?: boolean
  dispositionForm: DispositionStateType
  setDispositionForm: React.Dispatch<React.SetStateAction<DispositionStateType>>
  setFormErrors: React.Dispatch<React.SetStateAction<DispositionFormErrorsType>>
  selectedDispositionRequiresReview?: boolean
  hasInboxAccess?: boolean
  incident?: Incident
  reportValidationFeedbackProps?: ReportValidationFeedbackProps
  isInvestigation?: boolean
}

const entityLabels = {
  INVESTIGATION: 'investigation',
  CASE: 'case',
}

// eslint-disable-next-line complexity, max-statements
export const DispositionBaseDrawerV2 = ({
  caseDispositionOptions,
  caseTask,
  caseWorkflowType,
  defaultDisposition,
  detectiveUnits,
  incidentDispositionOptions,
  isOpen,
  caseInboxRequiresReview = false,
  dispositionForm,
  setDispositionForm,
  setFormErrors,
  selectedDispositionRequiresReview,
  hasInboxAccess,
  incident,
  reportValidationFeedbackProps,
  isInvestigation,
}: Props) => {
  const { hasAttribute, getAttributeStringValue } = useUserContext()
  const [showAssignDetective, setShowAssignDetective] = React.useState(false)
  const hasEnableCaseStaticPermissions = hasAttribute(
    AgencyAttributesEnums.enableCaseStaticPermissions
  )
  const investigativeEntityString = isInvestigation ? entityLabels.INVESTIGATION : entityLabels.CASE

  const currentWorkflowStateType =
    last(caseTask.actions)?.workflowStateType ||
    mapDispositionToWorkflowState({ value: dispositionForm.status, label: '' })
  const currentWorkflowStateTypeLabel = WorkflowStateTypeLabel[currentWorkflowStateType]
  const currentOwner = React.useMemo(() => getLastOwner(caseTask.actions), [caseTask.actions])
  const assignLeadDetectiveText = React.useMemo(() => {
    const getButtonText = (titleString: string) => {
      return currentOwner
        ? __('Reassign $[titleString]', {
            titleString,
          })
        : __('Assign $[titleString]', {
            titleString,
          })
    }
    const isStandards = caseWorkflowType === WorkflowTypeEnum.StandardsCase
    const targetAttr = isStandards
      ? 'agencyInvestigatorStandardsTitle'
      : 'agencyInvestigatorRecordsTitle'
    const titleString = getAttributeStringValue(AgencyAttributesEnums[targetAttr])
    return titleString ? getButtonText(titleString) : getButtonText('lead detective')
  }, [caseWorkflowType, currentOwner, getAttributeStringValue])

  const { statusOptions = [], reasonOptions = [] } =
    getCaseEnumOptions(caseDispositionOptions) || {}
  const { clearanceOptions = [], ucrOptions = [] } =
    getIncidentEnumOptions(incidentDispositionOptions) || {}

  const defaultDispositionStatus = statusOptions.find(
    (option) => option.stateType === defaultDisposition
  )?.stateType
  const [selectedDispositionStatus, setSelectedDispositionStatus] = React.useState<
    string | undefined
  >(
    defaultDispositionStatus ||
      (defaultDisposition &&
        mapDispositionToWorkflowState({ value: defaultDisposition, label: '' })) ||
      currentWorkflowStateType
  )
  const selectedDispositionStatusLabel = WorkflowStateTypeLabel[selectedDispositionStatus || '']

  const { unitToStateIdsMap } = usePermittedCaseInboxes({
    caseWorkflowType,
  })
  const defaultSelectedDispositionStatus = statusOptions.find(
    (option) => option.value === defaultDisposition || option.value === dispositionForm.status
  )
  const [filteredReasonOptions, setFilteredReasonOptions] = React.useState(
    defaultSelectedDispositionStatus?.reasons || reasonOptions
  )

  React.useEffect(() => {
    if (!isOpen) {
      setDispositionForm((prevState) => ({
        ...prevState,
        status: defaultDisposition || dispositionForm.status,
        reason: dispositionForm.reason,
        unit: dispositionForm.unit,
        assignee: currentOwner as Detective,
        isUrgent: false,
      }))
      setSelectedDispositionStatus(
        defaultDispositionStatus ||
          (defaultDisposition &&
            mapDispositionToWorkflowState({ value: defaultDisposition, label: '' })) ||
          currentWorkflowStateType
      )
    }
  }, [
    isOpen,
    dispositionForm.unit,
    dispositionForm.status,
    dispositionForm.reason,
    defaultDisposition,
    defaultDispositionStatus,
    currentWorkflowStateType,
    currentOwner,
    setDispositionForm,
  ])

  const shouldShowClearanceFields = shouldShowClearanceData(dispositionForm.ucrDisposition)

  React.useEffect(() => {
    if (dispositionForm.ucrDisposition && !shouldShowClearanceFields) {
      setDispositionForm((prevState) => ({
        ...prevState,
        clearedExceptionally: undefined,
        clearanceDate: undefined,
      }))
    }
  }, [dispositionForm.ucrDisposition, setDispositionForm, shouldShowClearanceFields])

  const isReopening =
    currentWorkflowStateType === WorkflowStateType.CLOSED &&
    (selectedDispositionStatus === WorkflowStateType.TRIAGE ||
      selectedDispositionStatus === WorkflowStateType.ACTIVE)
  const isClosed =
    currentWorkflowStateType === WorkflowStateType.CLOSED &&
    selectedDispositionStatus === WorkflowStateType.CLOSED

  const shouldRequireUnitAndDetective =
    isReopening && (!dispositionForm.unit || !dispositionForm.assignee)

  const shouldShowAssignDetectiveForm =
    hasInboxAccess && !isClosed && (!!dispositionForm?.unit || isReopening)

  React.useEffect(() => {
    if (shouldRequireUnitAndDetective && !dispositionForm.assignee?.id) {
      setFormErrors((prevState) => ({
        ...prevState,
        assignee: __('Unit and lead detective must be set.'),
      }))
    } else if (currentOwner?.id && !dispositionForm.assignee?.id) {
      setFormErrors((prevState) => ({
        ...prevState,
        assignee: __(
          'To unassign lead detective, please use "Reassign $[investigativeEntityString]" under more actions dropdown.',
          {
            investigativeEntityString: startCase(investigativeEntityString),
          }
        ),
      }))
    } else {
      setFormErrors({})
    }
  }, [
    currentOwner?.id,
    dispositionForm.assignee?.id,
    setFormErrors,
    shouldRequireUnitAndDetective,
    investigativeEntityString,
  ])

  const caseStatusUpdateText =
    currentWorkflowStateTypeLabel !== selectedDispositionStatusLabel
      ? __(
          'If the disposition change is approved, the $[investigativeEntityString] status will change from $[currentWorkflowStateTypeLabel] to $[selectedDispositionStatusLabel].',
          {
            investigativeEntityString,
            currentWorkflowStateTypeLabel,
            selectedDispositionStatusLabel,
          }
        )
      : ''

  // TODO (RMS-47346): Remove legacy usage after migration:
  // - UserAccessEnum.CasesUpdateDispositionExternal
  // - UserAccessEnum.CasesUpdateDispositionInternal
  // - UserAccessEnum.UpdateDisposition
  const {
    access: {
      casesUpdateDispositionExternal,
      recordsCasesUpdateDispositionExternal,

      casesUpdateDispositionInternal,
      recordsCasesUpdateDispositionInternal,
      standardsCasesUpdateDispositionInternal,

      updateDisposition,
    },
    loading,
  } = useUserAccess([
    UserAccessEnum.CasesUpdateDispositionExternal,
    UserAccessEnum.RecordsCasesUpdateDispositionExternal,

    UserAccessEnum.CasesUpdateDispositionInternal,
    UserAccessEnum.RecordsCasesUpdateDispositionInternal,
    UserAccessEnum.StandardsCasesUpdateDispositionInternal,

    UserAccessEnum.UpdateDisposition,
  ])

  if (loading) {
    return null
  }

  const isRecords = caseWorkflowType === WorkflowTypeEnum.Case

  const canUpdateExternal = hasEnableCaseStaticPermissions
    ? (isRecords && recordsCasesUpdateDispositionExternal) ||
      casesUpdateDispositionExternal ||
      updateDisposition
    : true

  const userHasUpdateInternalDispositionPrivileges =
    casesUpdateDispositionInternal ||
    (isRecords ? recordsCasesUpdateDispositionInternal : standardsCasesUpdateDispositionInternal)
  const canUpdateInternal = hasEnableCaseStaticPermissions
    ? userHasUpdateInternalDispositionPrivileges || updateDisposition
    : true

  const setFormErrorsFromExceptionalClearanceForm = (errorPayload: object) => {
    setFormErrors((prevState) => omitBy({ ...prevState, ...errorPayload }, isUndefined))
  }

  return (
    <Grid>
      <Label marginBottom="S">{__('Internal Disposition')}</Label>
      {canUpdateInternal ? (
        <NextRadioGroup
          name="internal disposition"
          margin="0 0 L"
          onChange={(e) => {
            setDispositionForm((prevState) => ({
              ...prevState,
              status: e.target.value,
            }))
            const selectedDispositionOption = statusOptions.find(
              (option) => option.value === e.target.value
            )
            setSelectedDispositionStatus(
              selectedDispositionOption && mapDispositionToWorkflowState(selectedDispositionOption)
            )
            setFilteredReasonOptions(selectedDispositionOption?.reasons || reasonOptions)
            if (selectedDispositionOption) {
              setDispositionForm((prevState) => ({
                ...prevState,
                reason: '',
              }))
            }
          }}
          selectedValue={dispositionForm.status}
        >
          {statusOptions.map(
            ({ label, value, requiresReview: dispositionStatusRequiresReview }) => {
              return (
                <NextRadioGroup.Item
                  label={label}
                  value={value}
                  key={value}
                  description={
                    caseInboxRequiresReview &&
                    selectedDispositionRequiresReview &&
                    dispositionStatusRequiresReview &&
                    dispositionForm.status === value
                      ? __(
                          'A supervisor must review the $[investigativeEntityString] before the disposition is updated. $[caseStatusUpdateText]',
                          {
                            investigativeEntityString,
                            caseStatusUpdateText,
                          }
                        )
                      : undefined
                  }
                />
              )
            }
          )}
        </NextRadioGroup>
      ) : (
        <Grid marginBottom="L">
          <Body1>
            {statusOptions.find((option) => option.value === dispositionForm.status)?.label || '–'}
          </Body1>
        </Grid>
      )}
      <NextAutocomplete
        label={__('Internal Disposition Reason')}
        value={filteredReasonOptions.find((option) => option.value === dispositionForm.reason)}
        onChange={(selectedOption) => {
          if (selectedOption) {
            setDispositionForm((prevState) => ({
              ...prevState,
              reason: selectedOption.value,
            }))
          }
        }}
        options={filteredReasonOptions}
        margin="0 0 L"
        readOnly={!canUpdateInternal}
      />
      {caseWorkflowType === WorkflowTypeEnum.Case && !!ucrOptions.length && (
        <>
          <Label marginBottom="S">{__('NIBRS Disposition')}</Label>
          {canUpdateExternal ? (
            <NextRadioGroup
              name="ucr disposition"
              margin="0 0 S"
              selectedValue={dispositionForm.ucrDisposition || DEFAULT_UCR_DISPOSITION.value}
              onChange={(e) =>
                setDispositionForm((prevState) => ({
                  ...prevState,
                  ucrDisposition: e.target.value,
                }))
              }
            >
              {ucrOptions.map(({ label, value }) => (
                <NextRadioGroup.Item label={label} value={value} key={value} />
              ))}
            </NextRadioGroup>
          ) : (
            <Grid marginBottom="S">
              <Body1>
                {ucrOptions.find((option) => option.value === dispositionForm.ucrDisposition)
                  ?.label || DEFAULT_UCR_DISPOSITION.label}
              </Body1>
            </Grid>
          )}
        </>
      )}
      {shouldShowClearanceFields && (
        <Stack width="100%" paddingLeft="xl" paddingTop="xxs" paddingBottom="l">
          {reportValidationFeedbackProps && (
            <ReportValidationFeedback {...reportValidationFeedbackProps} />
          )}
          <ExceptionalClearanceForm
            clearanceOptions={clearanceOptions}
            dispositionForm={dispositionForm}
            readOnly={!canUpdateExternal}
            setDispositionForm={setDispositionForm}
            onFieldValidation={setFormErrorsFromExceptionalClearanceForm}
            incidentDate={incident?.incidentDate}
          />
        </Stack>
      )}
      {shouldShowAssignDetectiveForm && (
        <>
          {!showAssignDetective && (
            <NextButton
              text={assignLeadDetectiveText}
              variant={NextButton.variants.flatPrimary}
              onClick={() => setShowAssignDetective(true)}
            />
          )}

          {showAssignDetective && (
            <>
              <Stack
                direction="row"
                width="100%"
                alignItems="center"
                justifyContent="space-between"
              >
                <Title>{assignLeadDetectiveText}</Title>
                <NextButton
                  text={__('Cancel')}
                  variant={NextButton.variants.flatCritical}
                  onClick={() => setShowAssignDetective(false)}
                />
              </Stack>
              {selectedDispositionRequiresReview && (
                <Body2 marginBottom="L" textColor={TextColor.Secondary}>
                  {__('The assignment will occur once the disposition review is approved.')}
                </Body2>
              )}
              <AssignDetectiveForm
                assigneeLabel={assignLeadDetectiveText}
                caseWorkflowType={caseWorkflowType}
                detectiveUnits={detectiveUnits}
                action={CaseActions.ASSIGN}
                assignment={dispositionForm || {}}
                onUpdateAssignment={(updatedAssignment) =>
                  setDispositionForm((prevState) => ({ ...prevState, ...updatedAssignment }))
                }
                onFormError={(hasError, errorMessage) => {
                  setFormErrors((prevState) =>
                    omitBy(
                      {
                        ...prevState,
                        dueDate: hasError ? errorMessage : undefined,
                      },
                      isUndefined
                    )
                  )
                }}
                unitToStateIdsMap={unitToStateIdsMap}
              />
            </>
          )}
        </>
      )}
    </Grid>
  )
}
