import * as React from 'react'
import { render, screen } from 'test/utils'
import { mockUseUserContext } from 'test/mocks/userContext'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { DispositionCaseDrawerV2 } from './dispositionCaseDrawerV2'

// Mock dependencies
jest.mock('shared/queries', () => ({
  useUpdateCaseDispositionMutation: () => [jest.fn(), { loading: false }],
  getActivityLogsGQLName: jest.fn(() => 'activityLogs'),
}))

jest.mock('pages/cases/components/dispositionDrawer/components/dispositionBaseDrawerV2', () => ({
  DispositionBaseDrawerV2: () => <div data-testid="single-unit-disposition">Single Unit</div>,
}))

jest.mock('iris-styleguide', () => ({
  ...jest.requireActual('iris-styleguide'),
  NextTabsProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="multi-unit-tabs">{children}</div>,
}))

describe('DispositionCaseDrawerV2 - Multi-Unit Feature Flag', () => {
  const mockCaseData = {
    id: 'test-case-id',
    detectiveUnits: ['Major Crimes'],
    unitAssignments: [
      { unitId: 'major-crimes', unitName: 'Major Crimes', isPrimary: true }
    ],
    dispositions: [],
    task: { id: 'task-id' },
  }

  const defaultProps = {
    isOpen: true,
    caseData: mockCaseData,
    onClose: jest.fn(),
    defaultDisposition: {},
  }

  it('should render single unit when feature flag is disabled', () => {
    mockUseUserContext({
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => 
          attr !== AgencyAttributesEnums.enableMultiUnitCase,
      },
    })

    render(<DispositionCaseDrawerV2 {...defaultProps} />)

    expect(screen.getByTestId('single-unit-disposition')).toBeInTheDocument()
    expect(screen.queryByTestId('multi-unit-tabs')).not.toBeInTheDocument()
  })

  it('should render multi-unit tabs when feature flag is enabled', () => {
    mockUseUserContext({
      overrides: {
        hasAttribute: (attr: AgencyAttributesEnums) => 
          attr === AgencyAttributesEnums.enableMultiUnitCase,
      },
    })

    render(<DispositionCaseDrawerV2 {...defaultProps} />)

    expect(screen.getByTestId('multi-unit-tabs')).toBeInTheDocument()
    expect(screen.queryByTestId('single-unit-disposition')).not.toBeInTheDocument()
  })
})
