/* eslint-disable max-lines */
import { Enums } from '@axon-enterprise/schemautils'
import * as React from 'react'
import { first, identity, isEmpty, isEqual, last } from 'lodash'
import { Body1, <PERSON>rror<PERSON>ialog, NextButton, NextDrawer, useIncidentContext } from 'iris-styleguide'
import { Card, Stack } from '@axon-enterprise/spark'
import { Case, Detective } from 'pages/cases/types'
import { TaskPriorityEnum, UserAccessEnum, WorkflowTypeEnum } from 'static/graphql/types'
import { getActivityLogsGQLName, useUpdateCaseDispositionMutation } from 'shared/queries'
import { getDispositionReviewInfo } from 'shared/disposition/dispositionRequiresReview'
import {
  DEFAULT_UCR_DISPOSITION,
  getCaseEnumOptions,
  shouldShowClearanceData,
} from 'shared/disposition'
import { useCaseToolAccess, useUserAccess } from 'shared/hooks'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { useUserContext } from 'store/context'
import {
  CaseDispositionDrawers,
  DispositionFormErrorsType,
  hasExceptionalClearanceChanges,
  hasNibrsFieldChanges,
  initialDispositionFormState,
  isTaskCompleted,
  isTaskPendingReview,
} from './utils'
import { DispositionStateType } from './types'
import { DEFAULT_STATUS } from './constants'
import { DispositionBaseDrawerV2 } from './dispositionBaseDrawerV2'
import { PendingDispositionReviewDrawer } from './pendingDispositionReviewDrawer'
import { ReassignDispositionReviewDrawer } from './reassignDispositionReviewDrawer'
import {
  RequestFastForwardedNibrsReviewDrawer,
  RequestSupervisorReviewDrawer,
} from './requestDispositionReview'
import { DispositionReviewFeedback } from './components'
import { ReviewFooterActions } from './reviewFooterActions'
import {
  LogUcrDispositionOnCreateReviewProps,
  getReviewSupportText,
  logUcrDispositionOnCreateReview,
} from './dispositionCaseDrawerV2.logic'
import { useBypassSupervisorReview, useValidationMessagesFromGOReport } from './hooks'
import { getUcrDispositionForUpdate } from './requestDispositionReview/utils'

type Props = {
  isOpen: boolean
  caseData: Case
  caseDataRefetch?: () => void
  defaultDisposition?: string
  onClose: () => void
  showApproveCaseDispositionDrawer?: () => void
  showRejectCaseDispositionDrawer?: () => void
  assignCaseDispositionToUser?: () => void
  assignCaseDispositionToUserLoading?: boolean
}

// eslint-disable-next-line max-statements,complexity
export const DispositionCaseDrawerV2 = ({
  isOpen,
  caseData,
  caseDataRefetch,
  onClose,
  defaultDisposition,
  showApproveCaseDispositionDrawer,
  showRejectCaseDispositionDrawer,
  assignCaseDispositionToUser,
  assignCaseDispositionToUserLoading,
}: Props) => {
  const { hasAttribute } = useUserContext()
  const { hasInboxAccess } = useCaseToolAccess(caseData)
  const [dispositionForm, setDispositionForm] = React.useState(initialDispositionFormState)
  const [formErrors, setFormErrors] = React.useState<DispositionFormErrorsType>({})
  const [showError, setShowError] = React.useState<boolean>(false)
  const [isSaving, setIsSaving] = React.useState(false)
  const [activeDrawer, setActiveDrawer] = React.useState<
    keyof typeof CaseDispositionDrawers | undefined
  >(undefined)
  const { detectiveUnits, dispositions, task, owner, priority } = caseData
  const latestDisposition = last(dispositions)
  const [updateCaseDispositionMutation] = useUpdateCaseDispositionMutation()
  const {
    caseDispositionOptions,
    caseDocumentsRefetch,
    caseInboxes,
    incident,
    incidentDispositionOptions,
    incidentRefetch,
  } = useIncidentContext()
  const { statusOptions = [] } = getCaseEnumOptions(caseDispositionOptions) || {}

  const { data: incidentData } = incident || {}

  const currentDisposition: DispositionStateType = React.useMemo(
    () => ({
      status: latestDisposition?.status || DEFAULT_STATUS,
      reason: latestDisposition?.reason || '',
      assignee: owner as Detective,
      unit: first(detectiveUnits),
      clearedExceptionally: incidentData?.axon?.clearedExceptionally,
      clearanceDate: incidentData?.axon?.clearanceDate,
      ucrDisposition: incidentData?.axon?.ucrDisposition || DEFAULT_UCR_DISPOSITION.value,
      dueAt: task.dueAt,
      isUrgent: priority === TaskPriorityEnum.Urgent,
    }),
    [
      detectiveUnits,
      incidentData?.axon?.clearanceDate,
      incidentData?.axon?.clearedExceptionally,
      incidentData?.axon?.ucrDisposition,
      latestDisposition?.reason,
      latestDisposition?.status,
      owner,
      priority,
      task.dueAt,
    ]
  )

  React.useEffect(() => {
    setActiveDrawer(undefined)
    if (defaultDisposition) {
      setDispositionForm({ ...currentDisposition, status: defaultDisposition })
    } else {
      setDispositionForm(currentDisposition)
    }
  }, [isOpen, currentDisposition, defaultDisposition])

  const onCaseDispositionUpdate = async (closeDrawer: () => void) => {
    setIsSaving(true)

    await updateCaseDispositionMutation({
      variables: {
        caseId: caseData.id,
        status: dispositionForm.status,
        reason: dispositionForm.reason,
        date: new Date().toISOString(),
        assignedDetective: dispositionForm.assignee?.id,
        unit: dispositionForm.unit,
        clearedExceptionally: dispositionForm.clearedExceptionally,
        clearanceDate: dispositionForm.clearanceDate,
        ucrDisposition: getUcrDispositionForUpdate({
          incidentUcrDisposition: incidentData?.axon?.ucrDisposition,
          formUcrDisposition: dispositionForm.ucrDisposition,
        }),
        taskComment: dispositionForm.comment,
        taskDueDate: dispositionForm.dueAt,
        taskPriority: dispositionForm.isUrgent ? TaskPriorityEnum.Urgent : TaskPriorityEnum.Medium,
      },
      refetchQueries: [getActivityLogsGQLName],
    })

    setIsSaving(false)
    await Promise.all([caseDataRefetch?.(), incidentRefetch(), caseDocumentsRefetch()])
    closeDrawer()
  }

  const isInReview = isTaskPendingReview(caseData?.latestDispositionReviewTask)

  // TODO (RMS-47346): Remove legacy usage after migration:
  // - UserAccessEnum.CasesUpdateDispositionBypassReview
  const {
    access: { casesUpdateDispositionBypassReview, recordsCasesUpdateDispositionBypassReview },
    loading,
  } = useUserAccess([
    UserAccessEnum.CasesUpdateDispositionBypassReview,
    UserAccessEnum.RecordsCasesUpdateDispositionBypassReview,
  ])

  const userHasBypassReviewPrivilege =
    casesUpdateDispositionBypassReview || recordsCasesUpdateDispositionBypassReview

  const {
    numValidationErrors,
    latestGOReport,
    loading: nibrsValidationLoading,
  } = useValidationMessagesFromGOReport({
    incidentId: incident?.id,
    skip: !isOpen || loading,
  })

  const hasEnableHardValidationFeature = hasAttribute(
    AgencyAttributesEnums.enableHardValidationFeature
  )
  const shouldShowValidationFeedback = React.useMemo(
    () =>
      shouldShowClearanceData(dispositionForm.ucrDisposition) &&
      (currentDisposition.ucrDisposition !== dispositionForm.ucrDisposition ||
        hasExceptionalClearanceChanges({
          currentDisposition,
          dispositionForm,
        })),
    [currentDisposition, dispositionForm]
  )

  const disableButton = React.useMemo(() => {
    // Disable if there is no change
    if (isEqual(currentDisposition, dispositionForm)) {
      return true
    }

    if (hasEnableHardValidationFeature && shouldShowValidationFeedback && !!numValidationErrors) {
      return true
    }

    // Disable if the exceptional clearance form is invalid
    if (shouldShowClearanceData(dispositionForm.ucrDisposition)) {
      return [
        !dispositionForm.clearedExceptionally,
        dispositionForm.clearedExceptionally !==
          Enums.Incident.ClearedExceptionally.NOT_APPLICABLE && !dispositionForm.clearanceDate,
        !!formErrors.clearedExceptionally,
        !!formErrors.clearanceDate,
      ].some(identity)
    }

    return false
  }, [
    currentDisposition,
    dispositionForm,
    formErrors.clearanceDate,
    formErrors.clearedExceptionally,
    hasEnableHardValidationFeature,
    numValidationErrors,
    shouldShowValidationFeedback,
  ])

  const isNibrsReviewWorkflowEnabled = hasAttribute(
    AgencyAttributesEnums.enableCaseDispositionUpdateForRecordsWithNIBRSReview
  )

  const { canBypassSupervisorReview } = useBypassSupervisorReview()

  const requiresNibrsReview = React.useMemo(
    () =>
      isNibrsReviewWorkflowEnabled && hasNibrsFieldChanges({ currentDisposition, dispositionForm }),
    [isNibrsReviewWorkflowEnabled, currentDisposition, dispositionForm]
  )

  const { requiresReview, caseInboxRequiresReview } = React.useMemo(
    () =>
      getDispositionReviewInfo({
        workflowStateId: caseData.workflowStateId,
        inboxes: caseInboxes,
        currentUnit: currentDisposition.unit,
        statusOptions,
        selectedStatus: dispositionForm.status,
        currentStatus: currentDisposition.status,
        userHasBypassReviewPrivilege,
        loading,
        requiresNibrsReview,
      }),
    [
      caseData.workflowStateId,
      caseInboxes,
      currentDisposition.unit,
      statusOptions,
      dispositionForm.status,
      currentDisposition.status,
      userHasBypassReviewPrivilege,
      loading,
      requiresNibrsReview,
    ]
  )

  const baseLogUcrDispositionProps: Omit<
    LogUcrDispositionOnCreateReviewProps,
    'initiatedFrom' | 'requestDisposition'
  > = React.useMemo(
    () => ({
      caseId: caseData.id,
      friendlyId: caseData.incident.friendlyId,
      formDisposition: dispositionForm,
      currentDisposition,
      incidentData,
    }),
    [caseData.id, caseData.incident.friendlyId, dispositionForm, currentDisposition, incidentData]
  )

  if (loading) {
    return null
  }

  const hasPreviousApprovalOrRejection = isTaskCompleted(caseData.latestDispositionReviewTask)
  const reviewSupportText = getReviewSupportText(caseData.latestDispositionReviewTask)

  const handleNextStepClick = (closeDrawer: () => void) => {
    if (!isEmpty(formErrors)) {
      setShowError(true)
      setIsSaving(false)
      return
    }

    if (!requiresReview) {
      onCaseDispositionUpdate(closeDrawer)
      return
    }

    if (canBypassSupervisorReview && requiresNibrsReview) {
      setActiveDrawer(CaseDispositionDrawers.RequestFastForwardedNibrsReviewDrawer)
    } else {
      setActiveDrawer(CaseDispositionDrawers.RequestSupervisorReviewDrawer)
    }
  }

  return (
    <NextDrawer
      title={__('Update Case Disposition - $[friendlyId]', {
        friendlyId: caseData.incident.friendlyId,
      })}
      open={isOpen}
      onClose={onClose}
      primaryAction={({ closeDrawer }) =>
        isInReview ? (
          <ReviewFooterActions
            caseData={caseData}
            closeDrawer={closeDrawer}
            setActiveDrawer={setActiveDrawer}
            showApproveCaseDispositionDrawer={showApproveCaseDispositionDrawer}
            showRejectCaseDispositionDrawer={showRejectCaseDispositionDrawer}
            assignCaseDispositionToUser={assignCaseDispositionToUser}
            assignCaseDispositionToUserLoading={assignCaseDispositionToUserLoading}
          />
        ) : (
          <NextButton
            text={requiresReview ? __('Next') : __('Set Case Disposition')}
            loading={isSaving || nibrsValidationLoading}
            disabled={disableButton}
            onClick={() => handleNextStepClick(closeDrawer)}
          />
        )
      }
      supportText={isInReview ? reviewSupportText : ''}
    >
      {isInReview ? (
        <PendingDispositionReviewDrawer
          isOpen={isOpen}
          onClose={() => setActiveDrawer(undefined)}
          caseData={caseData}
          caseDispositionOptions={caseDispositionOptions}
          incidentDispositionOptions={incidentDispositionOptions}
          currentDisposition={currentDisposition}
        />
      ) : (
        <Stack space="l">
          {hasPreviousApprovalOrRejection && hasInboxAccess && (
            <Card>
              <DispositionReviewFeedback caseData={caseData} />
            </Card>
          )}
          <DispositionBaseDrawerV2
            isOpen={isOpen}
            caseWorkflowType={WorkflowTypeEnum.Case}
            caseTask={task}
            incident={incident}
            caseDispositionOptions={caseDispositionOptions}
            caseInboxRequiresReview={caseInboxRequiresReview}
            incidentDispositionOptions={incidentDispositionOptions}
            defaultDisposition={defaultDisposition}
            detectiveUnits={detectiveUnits}
            dispositionForm={dispositionForm}
            setDispositionForm={setDispositionForm}
            setFormErrors={setFormErrors}
            selectedDispositionRequiresReview={requiresReview}
            hasInboxAccess={hasInboxAccess}
            reportValidationFeedbackProps={{
              numValidationErrors,
              latestGOReport,
              loading: nibrsValidationLoading,
              shouldShowValidationFeedback,
              hasEnableHardValidationFeature,
            }}
          />
        </Stack>
      )}
      <RequestFastForwardedNibrsReviewDrawer
        isOpen={activeDrawer === CaseDispositionDrawers.RequestFastForwardedNibrsReviewDrawer}
        onClose={() => setActiveDrawer(undefined)}
        onComplete={() => {
          caseDataRefetch?.()
          incidentRefetch?.()
          onClose()
        }}
        caseId={caseData.id}
        caseDispositionOptions={caseDispositionOptions}
        incidentDispositionOptions={incidentDispositionOptions}
        currentDisposition={currentDisposition}
        dispositionForm={dispositionForm}
        // TODO (bhammond): Remove once mycroft is updated to handle UCR non-update scenario (RMS-69035)
        incidentUcrDisposition={incidentData?.axon?.ucrDisposition}
        logUcrDisposition={(requestDisposition) =>
          logUcrDispositionOnCreateReview({
            ...baseLogUcrDispositionProps,
            requestDisposition,
            initiatedFrom: 'Request fast forwarded NIBRS review drawer',
          })
        }
      />
      <RequestSupervisorReviewDrawer
        isOpen={activeDrawer === CaseDispositionDrawers.RequestSupervisorReviewDrawer}
        onClose={() => setActiveDrawer(undefined)}
        onComplete={() => {
          caseDataRefetch?.()
          incidentRefetch?.()
          onClose()
        }}
        caseId={caseData.id}
        caseDispositionOptions={caseDispositionOptions}
        incidentDispositionOptions={incidentDispositionOptions}
        currentDisposition={currentDisposition}
        dispositionForm={dispositionForm}
        isSeparateNibrsReviewRequired={requiresNibrsReview}
        // TODO (bhammond): Remove once mycroft is updated to handle UCR non-update scenario (RMS-69035)
        incidentUcrDisposition={incidentData?.axon?.ucrDisposition}
        logUcrDisposition={(requestDisposition) =>
          logUcrDispositionOnCreateReview({
            ...baseLogUcrDispositionProps,
            requestDisposition,
            initiatedFrom: 'Request supervisor review drawer',
          })
        }
      />
      {!!caseData.latestDispositionReviewTask && (
        <ReassignDispositionReviewDrawer
          isOpen={activeDrawer === CaseDispositionDrawers.ReassignDispositionReviewDrawer}
          dispositionReviewTask={caseData.latestDispositionReviewTask}
          onClose={() => {
            caseDataRefetch?.()
            setActiveDrawer(undefined)
          }}
        />
      )}
      <ErrorDialog
        onClose={() => setShowError(false)}
        open={showError}
        primaryButtonText={__('OK')}
        title={__('Error')}
      >
        {formErrors &&
          Object.entries(formErrors).map(([key, value]) => <Body1 key={key}>{value}</Body1>)}
      </ErrorDialog>
    </NextDrawer>
  )
}
