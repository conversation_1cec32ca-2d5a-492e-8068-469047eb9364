/* eslint-disable max-lines */
import * as React from 'react'
import { isEmpty } from 'lodash'
import { Body1, NextButton, NextDrawer, NextTabsProvider, NextTabBar, NextTabPanel, ButtonV<PERSON>t, __ } from 'iris-styleguide'
import { <PERSON><PERSON>, Card } from '@axon-enterprise/spark'
import { Case, UnitAssignment } from 'pages/cases/types'
import { TaskPriorityEnum } from 'static/graphql/types'
import { DispositionCaseV3Unit } from './dispositionCaseV3Unit'

type Props = {
  isOpen: boolean
  caseData: Case
  caseDataRefetch?: () => void
  onClose: () => void
  defaultDisposition?: string
  showApproveCaseDispositionDrawer?: () => void
  showRejectCaseDispositionDrawer?: () => void
  assignCaseDispositionToUser?: () => void
  assignCaseDispositionToUserLoading?: boolean
}

// Mock data generator for demonstration - this will be replaced by actual data mapping
const generateMockUnitAssignments = (caseData: Case): UnitAssignment[] => {
  // For now, generate mock data based on the requirements
  // In production, this would come from the actual case data mapping
  return [
    {
      unitId: 'major-crimes-unit-id',
      unitName: 'Major Crimes',
      detectiveId: 'john-smith-uuid',
      detectiveName: '<PERSON>',
      priority: TaskPriorityEnum.High,
      dueAt: '2024-02-01T00:00:00Z',
      status: 'OPEN',
      workflowStateId: 'active-state-id',
      workflowStateType: 'ACTIVE',
      isPrimary: true,
      comment: 'Lead investigation - armed robbery with multiple suspects',
      assignee: caseData.owner as any,
      isUrgent: true,
    },
    {
      unitId: 'gangs-unit-id',
      unitName: 'Gangs Unit',
      detectiveId: 'jane-doe-uuid',
      detectiveName: 'Jane Doe',
      priority: TaskPriorityEnum.Medium,
      dueAt: '2024-01-30T00:00:00Z',
      status: 'PENDING_NIBRS_REVIEW',
      workflowStateId: 'review-state-id',
      workflowStateType: 'PENDING_REVIEW',
      isPrimary: false,
      comment: '🔍 NIBRS Review: Investigating gang connections - waiting for supervisor review',
      isUrgent: false,
    },
    {
      unitId: 'narcotics-unit-id',
      unitName: 'Narcotics Unit',
      detectiveId: 'bob-wilson-uuid',
      detectiveName: 'Bob Wilson',
      priority: TaskPriorityEnum.Low,
      dueAt: '2024-01-20T00:00:00Z',
      status: 'CLOSED',
      workflowStateId: 'closed-state-id',
      workflowStateType: 'CLOSED',
      isPrimary: false,
      comment: 'Drug angle investigation completed - no narcotics involvement found',
      isUrgent: false,
    },
    {
      unitId: 'cyber-crimes-unit-id',
      unitName: 'Cyber Crimes',
      detectiveId: 'alice-cyber-uuid',
      detectiveName: 'Alice Johnson',
      priority: TaskPriorityEnum.Medium,
      dueAt: '2024-01-28T00:00:00Z',
      status: 'OPEN',
      workflowStateId: 'active-state-id',
      workflowStateType: 'ACTIVE',
      isPrimary: false,
      comment: 'Digital forensics analysis of suspect devices',
      isUrgent: false,
    },
    {
      unitId: 'financial-crimes-unit-id',
      unitName: 'Financial Crimes',
      detectiveId: 'charlie-fin-uuid',
      detectiveName: 'Charlie Davis',
      priority: TaskPriorityEnum.Medium,
      dueAt: '2024-02-05T00:00:00Z',
      status: 'OPEN',
      workflowStateId: 'active-state-id',
      workflowStateType: 'ACTIVE',
      isPrimary: false,
      comment: 'Tracing financial transactions and money laundering',
      isUrgent: false,
    },
    {
      unitId: 'special-investigations-unit-id',
      unitName: 'Special Investigations',
      detectiveId: 'diana-special-uuid',
      detectiveName: 'Diana Rodriguez',
      priority: TaskPriorityEnum.High,
      dueAt: '2024-01-25T00:00:00Z',
      status: 'SUSPENDED',
      workflowStateId: 'suspended-state-id',
      workflowStateType: 'SUSPENDED',
      isPrimary: false,
      comment: 'Coordinating with federal agencies - awaiting clearance',
      isUrgent: true,
    },
  ]
}

// Check if any unit has NIBRS review in progress
const checkNibrsReviewStatus = (unitAssignments: UnitAssignment[]) => {
  const unitInReview = unitAssignments.find(
    (unit) => unit.status === 'PENDING_NIBRS_REVIEW' || unit.status === 'NIBRS_REVIEW'
  )
  const hasReviewInProgress = !!unitInReview

  return {
    hasReviewInProgress,
    message: hasReviewInProgress
      ? __('NIBRS disposition update blocked: $[unitName] is currently under review. Other units cannot update NIBRS fields until this review is complete.', {
          unitName: unitInReview?.unitName || 'Unknown Unit'
        })
      : undefined,
  }
}

export const DispositionCaseDrawerV3 = ({
  isOpen,
  caseData,
  caseDataRefetch,
  onClose,
  defaultDisposition,
  showApproveCaseDispositionDrawer,
  showRejectCaseDispositionDrawer,
  assignCaseDispositionToUser,
  assignCaseDispositionToUserLoading,
}: Props) => {

  // Get unit assignments - use mock data for now, replace with actual data mapping later
  const unitAssignments = React.useMemo(() => {
    if (caseData.unitAssignments && !isEmpty(caseData.unitAssignments)) {
      return caseData.unitAssignments
    }
    // Generate mock data for demonstration
    const mockUnits = generateMockUnitAssignments(caseData)
    return mockUnits
  }, [caseData])

  // Check NIBRS review status
  const nibrsStatus = React.useMemo(() => checkNibrsReviewStatus(unitAssignments), [unitAssignments])

  // Sort units with primary first, then by unit name
  const sortedUnits = React.useMemo(() => {
    const sorted = [...unitAssignments].sort((a, b) => {
      if (a.isPrimary && !b.isPrimary) return -1
      if (!a.isPrimary && b.isPrimary) return 1
      return a.unitName.localeCompare(b.unitName)
    })

    return sorted
  }, [unitAssignments])

  // State for active tab
  const [activeTabKey, setActiveTabKey] = React.useState<string>(
    sortedUnits[0]?.unitId || 'default'
  )



  // Set active tab to first unit when drawer opens
  React.useEffect(() => {
    if (isOpen && sortedUnits.length > 0) {

      setActiveTabKey(sortedUnits[0].unitId)
    }
  }, [isOpen, sortedUnits])

  // Handle tab change
  const handleTabChange = (tabKey: string) => {

    if (tabKey) {
      setActiveTabKey(tabKey)
    }
  }

  // Get the tab title with primary indicator
  const getTabTitle = (unit: UnitAssignment) => {
    return unit.isPrimary ? `${unit.unitName} (Primary)` : unit.unitName
  }

  // Generate truncated tab title for long names
  const getTruncatedTabTitle = (unit: UnitAssignment) => {
    const title = getTabTitle(unit)
    return title.length > 20 ? `${title.substring(0, 17)}...` : title
  }

  // Handle overflow tabs - if there are too many tabs, show "More" dropdown
  const MAX_VISIBLE_TABS = 4 // With 6 units, we'll have 4 visible + 2 in "More"
  const visibleUnits = sortedUnits.slice(0, MAX_VISIBLE_TABS)
  const overflowUnits = sortedUnits.slice(MAX_VISIBLE_TABS)
  const hasOverflow = overflowUnits.length > 0



  // Early return for debugging
  if (!isOpen) {
    return null
  }

  if (sortedUnits.length === 0) {
    return (
      <NextDrawer
        open={isOpen}
        onClose={onClose}
        title={__('Update Case Disposition - $[caseId]', {
          caseId: caseData.friendlyId,
        })}
      >
        <Stack space="l">
          <Card>
            <Body1>No unit assignments found for this case.</Body1>
          </Card>
        </Stack>
      </NextDrawer>
    )
  }

  return (
    <NextDrawer
      open={isOpen}
      onClose={onClose}
      title={__('Update Case Disposition - $[caseId]', {
        caseId: caseData.friendlyId,
      })}
    >
      <Stack space="l">
        {/* Multi-unit tabs */}
        <NextTabsProvider
          value={{
            activeKey: activeTabKey,
            onChange: handleTabChange,
          }}
        >
          {/* Tab bar with overflow handling */}
          <Stack space="s">
            <NextTabBar>
              {visibleUnits.map((unit) => {
                const title = getTruncatedTabTitle(unit)

                return (
                  <NextTabBar.Item
                    key={unit.unitId}
                    id={unit.unitId}
                    title={title}
                  />
                )
              })}
              {/* Show "More" tab if there are overflow units */}
              {hasOverflow && (
                <NextTabBar.Item
                  key="more"
                  id="more"
                  title={`More (${overflowUnits.length})`}
                />
              )}
            </NextTabBar>

            {/* Overflow units dropdown - shown when "More" tab is active */}
            {hasOverflow && activeTabKey === 'more' && (
              <Card>
                <Stack space="s">
                  <Body1>Additional Units:</Body1>
                  <Stack space="xs">
                    {overflowUnits.map((unit) => (
                      <NextButton
                        key={unit.unitId}
                        text={getTabTitle(unit)}
                        variant={ButtonVariant.secondary}
                        onClick={() => handleTabChange(unit.unitId)}
                      />
                    ))}
                  </Stack>
                </Stack>
              </Card>
            )}
          </Stack>

          {/* Tab panels for each unit */}
          {sortedUnits.map((unit, index) => {

            return (
              <NextTabPanel key={unit.unitId} id={unit.unitId}>
                <DispositionCaseV3Unit
                  caseData={caseData}
                  unitAssignment={unit}
                  caseDataRefetch={caseDataRefetch}
                  defaultDisposition={defaultDisposition}
                  showApproveCaseDispositionDrawer={showApproveCaseDispositionDrawer}
                  showRejectCaseDispositionDrawer={showRejectCaseDispositionDrawer}
                  assignCaseDispositionToUser={assignCaseDispositionToUser}
                  assignCaseDispositionToUserLoading={assignCaseDispositionToUserLoading}
                  nibrsBlocked={nibrsStatus.hasReviewInProgress && !unit.isPrimary}
                  nibrsBlockMessage={nibrsStatus.message}
                />
              </NextTabPanel>
            )
          })}

          {/* Empty state if no units */}
          {sortedUnits.length === 0 && (
            <NextTabPanel id="default">
              <Card>
                <Body1>No unit assignments found for this case.</Body1>
              </Card>
            </NextTabPanel>
          )}
        </NextTabsProvider>
      </Stack>
    </NextDrawer>
  )
}
