/* eslint-disable max-lines */
import { Enums } from '@axon-enterprise/schemautils'
import * as React from 'react'
import { first, identity, isEmpty, isEqual, last } from 'lodash'
import { Body1, ErrorDialog, NextButton, useIncidentContext, __ } from 'iris-styleguide'
import { Card, Stack } from '@axon-enterprise/spark'
import { Case, CaseDisposition, Detective, UnitAssignment } from 'pages/cases/types'
import { TaskPriorityEnum, UserAccessEnum, WorkflowTypeEnum } from 'static/graphql/types'
import { getActivityLogsGQLName, useUpdateCaseDispositionMutation } from 'shared/queries'
import { getDispositionReviewInfo } from 'shared/disposition/dispositionRequiresReview'
import {
  DEFAULT_UCR_DISPOSITION,
  getCaseEnumOptions,
  shouldShowClearanceData,
} from 'shared/disposition'
import { useCaseToolAccess, useUserAccess } from 'shared/hooks'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { useUserContext } from 'store/context'
import {
  CaseDispositionDrawers,
  DispositionFormErrorsType,
  hasExceptionalClearanceChanges,
  hasNibrsFieldChanges,
  initialDispositionFormState,
  isTaskCompleted,
  isTaskPendingReview,
} from './utils'
import { DispositionStateType } from './types'
import { DEFAULT_STATUS } from './constants'
import { DispositionBaseDrawerV2 } from './dispositionBaseDrawerV2'
import { PendingDispositionReviewDrawer } from './pendingDispositionReviewDrawer'
import { ReassignDispositionReviewDrawer } from './reassignDispositionReviewDrawer'
import {
  RequestFastForwardedNibrsReviewDrawer,
  RequestSupervisorReviewDrawer,
} from './requestDispositionReview'
import { DispositionReviewFeedback } from './components'
import { ReviewFooterActions } from './reviewFooterActions'
import {
  LogUcrDispositionOnCreateReviewProps,
  getReviewSupportText,
  logUcrDispositionOnCreateReview,
} from './dispositionCaseDrawerV2.logic'
import { useBypassSupervisorReview, useValidationMessagesFromGOReport } from './hooks'
import { getUcrDispositionForUpdate } from './requestDispositionReview/utils'

type Props = {
  caseData: Case
  unitAssignment: UnitAssignment
  caseDataRefetch?: () => void
  defaultDisposition?: string
  showApproveCaseDispositionDrawer?: () => void
  showRejectCaseDispositionDrawer?: () => void
  assignCaseDispositionToUser?: () => void
  assignCaseDispositionToUserLoading?: boolean
  nibrsBlocked?: boolean
  nibrsBlockMessage?: string
}

// This component handles the disposition logic for a single unit
// It's similar to DispositionCaseDrawerV2 but without the outer drawer wrapper
// eslint-disable-next-line max-statements,complexity
export const DispositionCaseV3Unit = ({
  caseData,
  unitAssignment,
  caseDataRefetch,
  defaultDisposition,
  showApproveCaseDispositionDrawer,
  showRejectCaseDispositionDrawer,
  assignCaseDispositionToUser,
  assignCaseDispositionToUserLoading,
  nibrsBlocked = false,
  nibrsBlockMessage,
}: Props) => {
  const { hasAttribute } = useUserContext()
  const { hasInboxAccess } = useCaseToolAccess(caseData)
  const [dispositionForm, setDispositionForm] = React.useState(initialDispositionFormState)
  const [formErrors, setFormErrors] = React.useState<DispositionFormErrorsType>({})
  const [showError, setShowError] = React.useState<boolean>(false)
  const [isSaving, setIsSaving] = React.useState(false)
  const [activeDrawer, setActiveDrawer] = React.useState<
    keyof typeof CaseDispositionDrawers | undefined
  >(undefined)
  
  // Use unit-specific data instead of case-wide data
  const { dispositions, task, priority } = caseData
  const latestDisposition = last(dispositions) as CaseDisposition | undefined
  const [updateCaseDispositionMutation] = useUpdateCaseDispositionMutation()
  
  const {
    caseDispositionOptions,
    caseDocumentsRefetch,
    caseInboxes,
    incident,
    incidentDispositionOptions,
    incidentRefetch,
  } = useIncidentContext()
  const { statusOptions = [] } = getCaseEnumOptions(caseDispositionOptions) || {}

  const { data: incidentData } = incident || {}

  // Create disposition state based on unit assignment
  const currentDisposition: DispositionStateType = React.useMemo(
    () => ({
      status: unitAssignment.status || DEFAULT_STATUS,
      reason: latestDisposition?.reason || '',
      assignee: unitAssignment.assignee as Detective,
      unit: unitAssignment.unitName,
      clearedExceptionally: incidentData?.axon?.clearedExceptionally,
      clearanceDate: incidentData?.axon?.clearanceDate,
      ucrDisposition: incidentData?.axon?.ucrDisposition || DEFAULT_UCR_DISPOSITION.value,
      dueAt: unitAssignment.dueAt || task.dueAt,
      isUrgent: unitAssignment.isUrgent || priority === TaskPriorityEnum.Urgent,
      comment: unitAssignment.comment,
    }),
    [
      unitAssignment,
      incidentData?.axon?.clearanceDate,
      incidentData?.axon?.clearedExceptionally,
      incidentData?.axon?.ucrDisposition,
      latestDisposition?.reason,
      task.dueAt,
      priority,
    ]
  )

  React.useEffect(() => {
    setActiveDrawer(undefined)
    if (defaultDisposition) {
      setDispositionForm({ ...currentDisposition, status: defaultDisposition })
    } else {
      setDispositionForm(currentDisposition)
    }
  }, [currentDisposition, defaultDisposition])

  const onCaseDispositionUpdate = async (closeDrawer: () => void) => {
    setIsSaving(true)

    await updateCaseDispositionMutation({
      variables: {
        caseId: caseData.id,
        unitId: unitAssignment.unitId, // Add unit-specific ID
        status: dispositionForm.status,
        reason: dispositionForm.reason,
        date: new Date().toISOString(),
        assignedDetective: dispositionForm.assignee?.id,
        unit: dispositionForm.unit,
        clearedExceptionally: dispositionForm.clearedExceptionally,
        clearanceDate: dispositionForm.clearanceDate,
        ucrDisposition: getUcrDispositionForUpdate({
          incidentUcrDisposition: incidentData?.axon?.ucrDisposition,
          formUcrDisposition: dispositionForm.ucrDisposition,
        }),
        taskComment: dispositionForm.comment,
        taskDueDate: dispositionForm.dueAt,
        taskPriority: dispositionForm.isUrgent ? TaskPriorityEnum.Urgent : TaskPriorityEnum.Medium,
      },
      refetchQueries: [getActivityLogsGQLName],
    })

    setIsSaving(false)
    await Promise.all([caseDataRefetch?.(), incidentRefetch(), caseDocumentsRefetch()])
    closeDrawer()
  }

  const isInReview = isTaskPendingReview(caseData?.latestDispositionReviewTask)

  // TODO (RMS-47346): Remove legacy usage after migration:
  // - UserAccessEnum.CasesUpdateDispositionBypassReview
  const {
    access: { casesUpdateDispositionBypassReview, recordsCasesUpdateDispositionBypassReview },
    loading,
  } = useUserAccess([
    UserAccessEnum.CasesUpdateDispositionBypassReview,
    UserAccessEnum.RecordsCasesUpdateDispositionBypassReview,
  ])

  const userHasBypassReviewPrivilege =
    casesUpdateDispositionBypassReview || recordsCasesUpdateDispositionBypassReview

  const {
    numValidationErrors,
    latestGOReport,
    loading: nibrsValidationLoading,
  } = useValidationMessagesFromGOReport({
    incidentId: incident?.id,
    skip: loading,
  })

  const hasEnableHardValidationFeature = hasAttribute(
    AgencyAttributesEnums.enableHardValidationFeature
  )
  
  const shouldShowValidationFeedback = React.useMemo(
    () =>
      shouldShowClearanceData(dispositionForm.ucrDisposition) &&
      (currentDisposition.ucrDisposition !== dispositionForm.ucrDisposition ||
        hasExceptionalClearanceChanges({
          currentDisposition,
          dispositionForm,
        })),
    [currentDisposition, dispositionForm]
  )

  const disableButton = React.useMemo(() => {
    // Disable if there is no change
    if (isEqual(currentDisposition, dispositionForm)) {
      return true
    }

    // Disable if NIBRS is blocked for this unit
    if (nibrsBlocked && shouldShowValidationFeedback) {
      return true
    }

    // If there are validation errors and hard validation is enabled
    if (hasEnableHardValidationFeature && numValidationErrors > 0) {
      return true
    }

    return false
  }, [
    currentDisposition,
    dispositionForm,
    nibrsBlocked,
    shouldShowValidationFeedback,
    hasEnableHardValidationFeature,
    numValidationErrors,
  ])

  const hasEnableCaseDispositionReview = hasAttribute(
    AgencyAttributesEnums.enableCaseDispositionReview
  )
  const hasEnableCaseDispositionUpdateForRecordsWithNIBRSReview = hasAttribute(
    AgencyAttributesEnums.enableCaseDispositionUpdateForRecordsWithNIBRSReview
  )

  const { canBypassSupervisorReview } = useBypassSupervisorReview()

  const requiresNibrsReview = React.useMemo(
    () =>
      hasEnableCaseDispositionUpdateForRecordsWithNIBRSReview && hasNibrsFieldChanges({ currentDisposition, dispositionForm }),
    [hasEnableCaseDispositionUpdateForRecordsWithNIBRSReview, currentDisposition, dispositionForm]
  )

  const { requiresReview, caseInboxRequiresReview } = React.useMemo(
    () =>
      getDispositionReviewInfo({
        workflowStateId: caseData.workflowStateId,
        inboxes: caseInboxes,
        currentUnit: currentDisposition.unit,
        statusOptions,
        selectedStatus: dispositionForm.status,
        currentStatus: currentDisposition.status,
        userHasBypassReviewPrivilege,
        loading,
        requiresNibrsReview,
      }),
    [
      caseData.workflowStateId,
      caseInboxes,
      currentDisposition.unit,
      statusOptions,
      dispositionForm.status,
      currentDisposition.status,
      userHasBypassReviewPrivilege,
      loading,
      requiresNibrsReview,
    ]
  )

  const getDrawerTitle = () => {
    if (activeDrawer) {
      switch (activeDrawer) {
        case CaseDispositionDrawers.PendingReview:
          return __('Pending Disposition Review')
        case CaseDispositionDrawers.ReassignReview:
          return __('Reassign Disposition Review')
        case CaseDispositionDrawers.RequestReview:
          return __('Request Supervisor Review')
        case CaseDispositionDrawers.RequestFastForwardedNibrsReview:
          return __('Request NIBRS Review')
        default:
          return __('Update Case Disposition')
      }
    }
    return __('Update Case Disposition')
  }

  const onClose = () => {
    setActiveDrawer(undefined)
    setShowError(false)
  }

  const reportValidationFeedbackProps = React.useMemo(() => {
    if (!shouldShowValidationFeedback) return undefined

    return {
      numValidationErrors,
      latestGOReport,
      loading: nibrsValidationLoading,
      shouldShowValidationFeedback,
      hasEnableHardValidationFeature,
    }
  }, [shouldShowValidationFeedback, numValidationErrors, latestGOReport, nibrsValidationLoading, hasEnableHardValidationFeature])

  const baseLogUcrDispositionProps: Omit<
    LogUcrDispositionOnCreateReviewProps,
    'initiatedFrom' | 'requestDisposition'
  > = React.useMemo(
    () => ({
      caseId: caseData.id,
      friendlyId: caseData.incident.friendlyId,
      formDisposition: dispositionForm,
      currentDisposition,
      incidentData,
    }),
    [caseData.id, caseData.incident.friendlyId, dispositionForm, currentDisposition, incidentData]
  )

  const hasPreviousApprovalOrRejection = isTaskCompleted(caseData?.latestDispositionReviewTask)
  const reviewSupportText = getReviewSupportText(caseData.latestDispositionReviewTask)

  const handleNextStepClick = React.useCallback((closeDrawer: () => void) => {
    if (!isEmpty(formErrors)) {
      setShowError(true)
      setIsSaving(false)
      return
    }

    if (!requiresReview) {
      onCaseDispositionUpdate(closeDrawer)
      return
    }

    if (canBypassSupervisorReview && requiresNibrsReview) {
      setActiveDrawer(CaseDispositionDrawers.RequestFastForwardedNibrsReview)
      logUcrDispositionOnCreateReview({
        ...baseLogUcrDispositionProps,
        requestDisposition: dispositionForm,
        initiatedFrom: 'Fast forwarded NIBRS review',
      })
      return
    }

    setActiveDrawer(CaseDispositionDrawers.RequestReview)
    logUcrDispositionOnCreateReview({
      ...baseLogUcrDispositionProps,
      requestDisposition: dispositionForm,
      initiatedFrom: 'Supervisor review',
    })
  }, [
    formErrors,
    requiresReview,
    canBypassSupervisorReview,
    requiresNibrsReview,
    baseLogUcrDispositionProps,
    dispositionForm,
    onCaseDispositionUpdate,
  ])

  const handleButtonClick = React.useCallback(async () => {
    setIsSaving(true)
    try {
      await handleNextStepClick(() => setActiveDrawer(undefined))
    } catch (error) {
      console.error('Failed to submit disposition:', error)
      setShowError(true)
    }
    setIsSaving(false)
  }, [handleNextStepClick])

  if (activeDrawer === CaseDispositionDrawers.PendingReview) {
    return (
      <PendingDispositionReviewDrawer
        isOpen
        onClose={onClose}
        caseData={caseData}
        caseDispositionOptions={caseDispositionOptions}
        incidentDispositionOptions={incidentDispositionOptions}
        currentDisposition={currentDisposition}
      />
    )
  }

  if (activeDrawer === CaseDispositionDrawers.ReassignReview) {
    return (
      <ReassignDispositionReviewDrawer
        isOpen
        caseData={caseData}
        caseDataRefetch={caseDataRefetch}
        onClose={onClose}
      />
    )
  }

  if (activeDrawer === CaseDispositionDrawers.RequestReview) {
    return (
      <RequestSupervisorReviewDrawer
        isOpen
        caseData={caseData}
        caseDataRefetch={caseDataRefetch}
        dispositionForm={dispositionForm}
        onClose={onClose}
        selectedDispositionRequiresReview={requiresReview}
        caseInboxRequiresReview={caseInboxRequiresReview}
        reviewSupportText={reviewSupportText}
      />
    )
  }

  if (activeDrawer === CaseDispositionDrawers.RequestFastForwardedNibrsReview) {
    return (
      <RequestFastForwardedNibrsReviewDrawer
        isOpen
        onClose={onClose}
        onComplete={() => {
          caseDataRefetch?.()
          incidentRefetch?.()
          // Note: Don't call onClose() here as this is not the outer drawer
        }}
        caseId={caseData.id}
        caseDispositionOptions={caseDispositionOptions}
        incidentDispositionOptions={incidentDispositionOptions}
        currentDisposition={currentDisposition}
        dispositionForm={dispositionForm}
        incidentUcrDisposition={incidentData?.axon?.ucrDisposition}
        logUcrDisposition={(requestDisposition) =>
          logUcrDispositionOnCreateReview({
            ...baseLogUcrDispositionProps,
            requestDisposition,
            initiatedFrom: 'Request fast forwarded NIBRS review drawer',
          })
        }
      />
    )
  }

  return (
    <Stack space="l">
      {/* NIBRS Blocked Warning */}
      {nibrsBlocked && (
        <Card>
          <Body1>
            {nibrsBlockMessage || 
              __('NIBRS disposition updates are currently blocked due to another unit being under review.')
            }
          </Body1>
        </Card>
      )}

      {/* Review feedback */}
      {hasPreviousApprovalOrRejection && hasInboxAccess && (
        <Card>
          <DispositionReviewFeedback caseData={caseData} />
        </Card>
      )}

      {/* Main form content */}
      <DispositionBaseDrawerV2
        isOpen
        caseWorkflowType={WorkflowTypeEnum.Case}
        caseTask={task}
        incident={incident}
        caseDispositionOptions={caseDispositionOptions}
        caseInboxRequiresReview={caseInboxRequiresReview}
        incidentDispositionOptions={incidentDispositionOptions}
        defaultDisposition={defaultDisposition}
        detectiveUnits={[unitAssignment.unitName]}
        dispositionForm={dispositionForm}
        setDispositionForm={setDispositionForm}
        setFormErrors={setFormErrors}
        selectedDispositionRequiresReview={requiresReview}
        hasInboxAccess={hasInboxAccess}
        reportValidationFeedbackProps={reportValidationFeedbackProps}
      />

      {/* Footer actions */}
      {isInReview ? (
        <ReviewFooterActions
          caseData={caseData}
          showApproveCaseDispositionDrawer={showApproveCaseDispositionDrawer}
          showRejectCaseDispositionDrawer={showRejectCaseDispositionDrawer}
        />
      ) : (
        <NextButton
          text={requiresReview ? __('Next') : __('Set Case Disposition')}
          loading={isSaving || nibrsValidationLoading}
          disabled={disableButton}
          onClick={handleButtonClick}
        />
      )}

      <ErrorDialog 
        open={showError} 
        onClose={() => setShowError(false)}
        message={__('Failed to update case disposition. Please try again.')}
      />
    </Stack>
  )
}
