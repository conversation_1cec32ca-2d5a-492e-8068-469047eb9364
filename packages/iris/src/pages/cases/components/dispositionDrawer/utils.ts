import moment from 'moment'
import { log } from 'shared/log'
import { WorkflowStateType } from 'pages/cases/enums'
import { Detective } from 'pages/cases/types'
import {
  CaseDispositionOption,
  CaseDispositionOptionsType,
  getDispositionValues,
} from 'shared/disposition'
import {
  CaseDispositionNIBRSReviewWorkflowStateTypes,
  CaseDispositionReviewWorkflowStateTypes,
} from 'shared/task'
import { Task, TaskAccessEnum, WorkflowTypeEnum } from 'static/graphql/types'
import { first, isEmpty, isEqual, last, pick } from 'lodash'
import { getNextTransitionToDispositionReviewStateType } from 'pages/cases/utils'
import { getCaseDispositionReview } from 'pages/cases/utils/getCaseDispositionReview'
import {
  ACTIVE_STATUS,
  DEFAULT_STATUS,
  NIBRS_REVIEW_PENDING_STATES,
  REVIEW_APPROVED_STATES,
  REVIEW_DECISION_STATES,
  REVIEW_INITIATED_STATES,
  REVIEW_PENDING_STATES,
  REVIEW_REJECTED_STATES,
  REVIEW_STATES,
  TERMINAL_REVIEW_STATES,
  nibrsClearanceFields,
  nibrsFields,
} from './constants'
import { NibrsFieldsType } from './types'

/**
 * Extract the disposition workflow state for the disposition option if value
 * is schema driven, fallback to hardcoded iris mapping otherwise
 *
 * @param dispositionOption
 */
export const mapDispositionToWorkflowState = (
  dispositionOption: CaseDispositionOption
): WorkflowStateType => {
  const schemaDrivenStateType = Object.values(WorkflowStateType).find(
    (value) => value === dispositionOption.stateType
  )
  if (schemaDrivenStateType) {
    return schemaDrivenStateType
  }

  if (dispositionOption.stateType && !schemaDrivenStateType) {
    log.error(
      `Disposition stateType ${dispositionOption.stateType} is not a valid WorkflowStateType for Case Workflow`
    )
  }

  if (ACTIVE_STATUS.includes(dispositionOption.value)) {
    return WorkflowStateType.ACTIVE
  }

  return WorkflowStateType.CLOSED
}

export type DispositionFormErrorsType = {
  assignee?: string
  clearedExceptionally?: string
  clearanceDate?: string
  dueDate?: string
}

export type DispositionStateType = {
  status: string
  reason: string
  assignee?: Detective
  unit?: string
  clearedExceptionally?: string
  clearanceDate?: string
  ucrDisposition?: string
  comment?: string
  dueAt?: string
  isUrgent?: boolean
}

export const initialDispositionFormState: DispositionStateType = {
  status: DEFAULT_STATUS,
  reason: '',
  assignee: undefined,
  unit: undefined,
  clearedExceptionally: undefined,
  clearanceDate: undefined,
  ucrDisposition: undefined,
  comment: undefined,
  dueAt: undefined,
  isUrgent: undefined,
}

export const CaseDispositionDrawers = {
  RequestFastForwardedNibrsReviewDrawer: 'RequestFastForwardedNibrsReviewDrawer',
  RequestSupervisorReviewDrawer: 'RequestSupervisorReviewDrawer',
  ReassignDispositionReviewDrawer: 'ReassignDispositionReviewDrawer',
} as const

export const isTaskPendingReview = (task?: Partial<Task>) =>
  REVIEW_PENDING_STATES.includes(task?.workflowState?.type || '')

export const isTaskRejected = (task?: Task) =>
  REVIEW_REJECTED_STATES.includes(task?.workflowState?.type || '')

export const isTaskPendingNibrsReview = (task?: Task) =>
  NIBRS_REVIEW_PENDING_STATES.includes(task?.workflowState?.type || '')

export const isTaskInReview = (task?: Task) =>
  REVIEW_STATES.includes(task?.workflowState?.type || '')

export const isTaskApproved = (task?: Task) =>
  REVIEW_APPROVED_STATES.includes(task?.workflowState?.type || '')

export const isTaskCompleted = (task?: Task) =>
  TERMINAL_REVIEW_STATES.includes(task?.workflowState?.type || '')

// Returns the latest action that initiated a disposition review request process
export const getLatestReviewRequestAction = (task?: Task) =>
  last(task?.actions?.filter((a) => REVIEW_INITIATED_STATES.includes(a.workflowState?.type || '')))

export const getLatestReviewDecisionAction = (task?: Task) =>
  last(task?.actions?.filter((a) => REVIEW_DECISION_STATES.includes(a?.workflowState?.type || '')))

export const getInitialCommentFromLatestReviewRequest = (task?: Task) => {
  const latestReviewRequestAction = getLatestReviewRequestAction(task)
  return first(latestReviewRequestAction?.comments)
}

export const isTaskRoutingCommentInitialCommentFromLatestReviewRequest = (task?: Task) => {
  const latestReviewRequestAction = getLatestReviewRequestAction(task)
  if (!task?.assignmentMeta || !latestReviewRequestAction) {
    return false
  }

  return task.assignmentMeta.comment?.id === first(latestReviewRequestAction.comments)?.id
}

export const getClearanceTypeValidationError = (selectedValue?: string) => {
  return selectedValue ? undefined : __('Exceptional Clearance Type is required')
}

export function getClearanceDateValidationError({
  selectedDate,
  incidentDate,
}: {
  selectedDate?: string
  incidentDate?: string
}) {
  if (!selectedDate) {
    return __('Exceptional Clearance Date Time is required')
  }
  if (incidentDate && moment(selectedDate).isSameOrBefore(moment(incidentDate))) {
    return __('Exceptional Clearance Date Time cannot be earlier than the incident date')
  }
  if (moment(selectedDate).isAfter(moment())) {
    return __('Exceptional Clearance Date Time cannot be in the future')
  }
  return undefined
}

export function getPendingDispositionDisplay({
  internalDispositionLabel,
  dispositionReviewTask,
  caseDispositionOptions,
}: {
  internalDispositionLabel: string | undefined
  dispositionReviewTask: Task | undefined
  caseDispositionOptions?: CaseDispositionOptionsType
}) {
  const hasDispositionInReview = isTaskInReview(dispositionReviewTask)
  const hasDispositionBeenApproved = isTaskApproved(dispositionReviewTask)
  const requestedDisposition =
    dispositionReviewTask && getCaseDispositionReview(dispositionReviewTask)
  const { internalDispositionWithReason: requestedDispositionLabel } = requestedDisposition
    ? getDispositionValues({
        caseDispositionOptions,
        disposition: requestedDisposition,
      })
    : { internalDispositionWithReason: undefined }
  const hasPendingDisposition =
    (hasDispositionInReview || hasDispositionBeenApproved) && !!requestedDisposition
  const pendingDisposition =
    requestedDispositionLabel && hasPendingDisposition
      ? ` ${internalDispositionLabel || __('None')} → ${requestedDispositionLabel}`
      : internalDispositionLabel
  return { hasPendingDisposition, pendingDisposition }
}

// Check if there are any changes in the NIBRS-related fields between the current disposition source
// and the form state
export const hasNibrsFieldChanges = ({
  currentDisposition,
  dispositionForm,
}: {
  currentDisposition?: Partial<DispositionStateType>
  dispositionForm: Partial<DispositionStateType>
}) => {
  if (isEmpty(currentDisposition)) {
    return false
  }

  const currNibrsValues: NibrsFieldsType = pick(currentDisposition, nibrsFields)
  const formNibrsValues: NibrsFieldsType = pick(dispositionForm, nibrsFields)

  return !isEqual(currNibrsValues, formNibrsValues)
}

export const hasExceptionalClearanceChanges = ({
  currentDisposition,
  dispositionForm,
}: {
  currentDisposition?: Partial<DispositionStateType>
  dispositionForm: Partial<DispositionStateType>
}) => {
  if (isEmpty(currentDisposition)) {
    return false
  }

  const currNibrsClearanceValues: NibrsFieldsType = pick(currentDisposition, nibrsClearanceFields)
  const formNibrsClearanceValues: NibrsFieldsType = pick(dispositionForm, nibrsClearanceFields)

  return !isEqual(currNibrsClearanceValues, formNibrsClearanceValues)
}

export const isTaskOnMultiReviewerWorkflow = (task?: Task) =>
  task?.workflow?.type === WorkflowTypeEnum.CaseDispositionNibrsReview

export const getApproveTaskTransition = (task: Task) => {
  if (isTaskOnMultiReviewerWorkflow(task)) {
    return getNextTransitionToDispositionReviewStateType({
      task,
      stateType:
        task.workflowState?.type === CaseDispositionNIBRSReviewWorkflowStateTypes.SUPERVISOR_REVIEW
          ? CaseDispositionNIBRSReviewWorkflowStateTypes.NIBRS_REVIEW
          : CaseDispositionNIBRSReviewWorkflowStateTypes.APPROVED,
    })
  }

  return getNextTransitionToDispositionReviewStateType({
    task,
    stateType: CaseDispositionReviewWorkflowStateTypes.APPROVED,
  })
}

export const getRejectTaskTransition = (task: Task) =>
  getNextTransitionToDispositionReviewStateType({
    task,
    stateType: isTaskOnMultiReviewerWorkflow(task)
      ? CaseDispositionNIBRSReviewWorkflowStateTypes.REJECTED
      : CaseDispositionReviewWorkflowStateTypes.REJECTED,
  })

export const getApproveAndRejectTaskTransitions = (task?: Task) =>
  task
    ? {
        approveTaskTransition: getApproveTaskTransition(task),
        rejectTaskTransition: getRejectTaskTransition(task),
      }
    : {
        approveTaskTransition: undefined,
        rejectTaskTransition: undefined,
      }

export const getTaskAssignmentOperations = (taskAccessOperations?: TaskAccessEnum[]) =>
  taskAccessOperations && !isEmpty(taskAccessOperations)
    ? {
        canAssignOthers: taskAccessOperations.includes(TaskAccessEnum.AssignTask),
        canAssignSelf: taskAccessOperations.includes(TaskAccessEnum.AssignSelfTask),
      }
    : { canAssignOthers: false, canAssignSelf: false }
