import { mapGetCaseQueryResponse, UnitAssignment } from './caseMapper'
import { GetCaseQuery } from 'static/graphql/types'

describe('caseMapper - Multi-Unit Feature', () => {
  const mockGetCaseQueryData: GetCaseQuery = {
    case: {
      id: 'test-case-id',
      txid: 'test-txid',
      friendlyId: 'CASE-2025-00001',
      caseFriendlyName: 'Test Case',
      data: { axon: {} },
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: '2025-01-01T00:00:00Z',
      tasks: {
        results: [{
          id: 'task-id',
          workflow: { id: 'workflow-id' },
          workflowState: { id: 'state-id', type: 'ACTIVE' },
        }]
      },
      incidents: [{
        id: 'incident-id',
        friendlyId: 'INC-2025-00001',
        data: {},
        evidence: [],
      }],
    },
  }

  const mockUnitToStateIdsMap = {}

  it('should not include investigative unit assignments when feature flag is disabled', () => {
    const result = mapGetCaseQueryResponse({
      data: mockGetCaseQueryData,
      unitToStateIdsMap: mockUnitToStateIdsMap,
      enableMultiUnitCase: false,
    })

    expect(result?.unitAssignments).toEqual([])
    expect(result?.detectiveUnits).toEqual([]) // Fallback to single unit logic
  })

  it('should include mock unit assignments when feature flag is enabled', () => {
    const result = mapGetCaseQueryResponse({
      data: mockGetCaseQueryData,
      unitToStateIdsMap: mockUnitToStateIdsMap,
      enableMultiUnitCase: true,
    })

    expect(result?.unitAssignments).toHaveLength(3)
    
    const assignments = result?.unitAssignments as UnitAssignment[]
    expect(assignments[0]).toEqual({
      unitId: 'major-crimes-unit-id',
      detectiveId: 'det-123',
      comment: 'Primary assignment for major crimes investigation',
      priority: 'HIGH',
      unitName: 'Major Crimes',
      detectiveName: 'John Smith',
      isPrimary: true,
    })

    expect(result?.detectiveUnits).toEqual(['Major Crimes', 'Gangs', 'Narcotics'])
    expect(result?.detectiveUnitsTitles).toEqual(['Major Crimes', 'Gangs', 'Narcotics'])
  })

  it('should use case-specific mock data when available', () => {
    const caseSpecificData = {
      ...mockGetCaseQueryData,
      case: {
        ...mockGetCaseQueryData.case!,
        id: 'specific-case-id',
      },
    }

    const result = mapGetCaseQueryResponse({
      data: caseSpecificData,
      unitToStateIdsMap: mockUnitToStateIdsMap,
      enableMultiUnitCase: true,
    })

    // Should still use default mock data since 'specific-case-id' isn't in our mock data
    expect(result?.unitAssignments).toHaveLength(3)
    expect(result?.detectiveUnits).toEqual(['Major Crimes', 'Gangs', 'Narcotics'])
  })
})
