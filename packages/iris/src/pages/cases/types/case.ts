import { CasePriority } from 'pages/cases/enums'
import { Activity, CaseUser, Detective, Incident, Note, Task } from 'pages/cases/types'
import { CaseDispositionOptionsType } from 'shared/disposition'
import { DataSchema, Task as HermesTask, TaskPriorityEnum } from 'static/graphql/types'

export type CaseDisposition = {
  status?: string
  reason?: string
  externalStatus?: string
  date?: string
  assignedDetective?: string
  unit?: string
  clearedExceptionally?: string
  clearanceDate?: string
  ucrDisposition?: string
}

// New type for multi-unit support
export type UnitAssignment = {
  unitId: string              // "major-crimes-unit-id"
  unitName: string            // "Major Crimes" 
  detectiveId?: string        // "john-smith-uuid"
  detectiveName?: string      // "<PERSON>"
  priority?: TaskPriorityEnum // "HIGH"
  dueAt?: string             // "2024-02-01T00:00:00Z"
  status?: string            // "OPEN" (from disposition)
  workflowStateId?: string   // "active-state-id"  
  workflowStateType?: string // "ACTIVE"
  isPrimary: boolean         // true for first unit
  comment?: string           // Assignment notes
  assignee?: Detective       // Full detective object
  isUrgent?: boolean        // Whether the task is urgent
}

export type Case = {
  id: string
  txid: string
  friendlyId: string
  caseFriendlyName?: string
  dispositions: Array<CaseDisposition>
  dispositionOptions?: CaseDispositionOptionsType
  createdAt: string
  incidentDate?: string
  updatedAt: string
  priority: CasePriority
  incident: Incident
  notes: Array<Note>
  activityLog: Array<Activity>
  owner?: CaseUser
  dueAt?: string
  detectiveUnits: Array<string>
  detectiveUnitsTitles?: Array<string>
  detectives: Array<Detective>
  workflowStateId?: string
  workflowStateType?: string
  shouldFastForwardWorkflow: boolean
  task: Task
  dataSchema?: DataSchema
  ecomCaseId?: string
  latestDispositionReviewTask?: HermesTask
  authorId?: string
  transferDate?: string
  hasArrest?: boolean
  // New field for multi-unit assignments
  unitAssignments?: Array<UnitAssignment>
}
