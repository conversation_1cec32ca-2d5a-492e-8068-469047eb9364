import { Detective } from 'pages/cases/types'

export type CaseAssignment = {
  caseFriendlyName?: string
  unit?: string
  assignee?: Detective
  comment?: string
  dueAt?: string
  isUrgent?: boolean
  title?: string
}

export type UnitAssignment = {
  unitId?: string
  unitName?: string
  assignee?: Detective
  comment?: string
  dueAt?: string
  isUrgent?: boolean
}

export type CaseAssignmentV2 = {
  caseId?: string
  units: UnitAssignment[]
}
