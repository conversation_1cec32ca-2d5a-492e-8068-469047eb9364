/* eslint-disable max-lines */
/* eslint-disable complexity */
/* eslint-disable max-statements */
import { ApolloQueryResult } from '@apollo/client'
import { axonEntityTypes } from '@axon-enterprise/schemautils'
import {
  ActiveCaseTaskData,
  AddActivityLogDrawer,
  AddCaseTaskDrawer,
  AddEvidenceDrawer,
  CaseNarrativeDrawer,
  DueDateDrawer,
  EditCaseTaskDrawer,
  ErrorBoundary,
  ErrorInitiatedFrom,
  EvidenceProvider,
  EvidenceUploadDrawer,
  IncidentEntityDrawer,
  LiftSealDrawer,
  NarrativeDrawer,
  RelatedIncidentsEventsDrawer,
  RestrictionDrawer,
  Sealing,
  Text,
  eligibleIncidentCaseEntityTypes,
  getEntitiesBySectionTitleMap,
  useListIncidentCaseEntitiesQuery,
  useQueryIncidentEntities,
  useTrackCaseTaskAddEvidence,
} from 'iris-styleguide'
import { compact, isEmpty, noop } from 'lodash'
import {
  AssignDetectiveCaseDrawer,
  AssignUnitCaseDrawer,
  CaseCreateDrawer,
  DispositionCaseDrawer,
  DispositionCaseDrawerV2,
  EditCaseFriendlyNameDrawer,
} from 'pages/cases/components'
import { DispositionCaseDrawerV3 } from 'pages/cases/components/dispositionDrawer/dispositionCaseDrawerV3'
import {
  ApproveDispositionDrawer,
  ReassignDispositionReviewDrawer,
  RejectDispositionDrawer,
} from 'pages/cases/components/dispositionDrawer'
import { Case, Task } from 'pages/cases/types'
import * as React from 'react'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { ecomEndpoints } from 'shared/ecom'
import { useCanImportPropertyEntitiesIntoCase, useUserAccess } from 'shared/hooks'
import { log } from 'shared/log'

import {
  DrawerType,
  IncidentCFS,
  IncidentEvidenceAttachment,
  IncidentIncident,
  formatIncidentEntities,
  mergeGroupedEntities,
  validEntityDrawerTypes,
} from 'shared/incident'
import { FolderEntityBaseDataType } from 'shared/folderEntity'
import { getActivityLogsGQLName } from 'shared/queries/getActivityLogs'
import { LiftSealParamsType } from 'shared/sealing'
import { hasInFlightTask } from 'shared/task'
import {
  ActivityLogRefTypeEnum,
  Document,
  Documents,
  EntityEnum,
  GetCaseDocumentsQuery,
  GetCaseDocumentsQueryVariables,
  IncidentEntity,
  RestrictionContextTypeEnum,
  SealScopeEnums,
  UserAccessEnum,
  WorkflowTypeEnum,
} from 'static/graphql/types'
import { useRestrictions } from 'store/context'
import { useUserContext } from 'store/context/user'
import { queryClient } from 'store/fetch'
import { shouldShowExpungeOption } from '../cfs/cfs.logic'
import {
  CaseSharingOptionsDrawer,
  Expungement,
  ExternalResponseFieldsDrawer,
  IncidentPrintOptionsDrawer,
  RelatedReportDrawer,
  RequestEvidenceDrawer,
} from './components'

export const availableDrawers: { [type in DrawerType]?: DrawerType } = {
  AddEvidence: 'AddEvidence',
  Attachment: 'Attachment',
  Restriction: 'Restriction',
  Sealing: 'Sealing',
  Narrative: 'Narrative',
  Entity: 'Entity',
  Expungement: 'Expungement',
  ExternalResponseFields: 'ExternalResponseFields',
  LiftSealing: 'LiftSealing',
  PrintOptions: 'PrintOptions',
  RequestEvidence: 'RequestEvidence',
  CreateCase: 'CreateCase',
  EditFriendlyCaseName: 'EditFriendlyCaseName',
  CaseDisposition: 'CaseDisposition',
  CaseDispositionApproval: 'CaseDispositionApproval',
  CaseDispositionRejection: 'CaseDispositionRejection',
  ReassignCase: 'ReassignCase',
  ReassignCaseDispositionReview: 'ReassignCaseDispositionReview',
  TransferUnit: 'TransferUnit',
  RelatedIncidents: 'RelatedIncidents',
  RelatedReport: 'RelatedReport',
  ActivityLog: 'ActivityLog',
  CaseSharing: 'CaseSharing',
  AddCaseTask: 'AddCaseTask',
  EditCaseTask: 'EditCaseTask',
  CaseSummary: 'CaseSummary',
  UpdateDueDate: 'UpdateDueDate',
}

type Props = {
  activeDrawer?: DrawerType
  activeIndex?: number
  activeEntityId?: string
  attachments?: Array<IncidentEvidenceAttachment>
  cfs?: IncidentCFS
  documents: Document[]
  caseFriendlyId?: string
  caseId?: string
  sectionTitle?: string
  isIncidentSealLifted: boolean
  incident: IncidentIncident
  liftSealParams: LiftSealParamsType
  caseData?: Case
  caseDocuments?: Documents
  caseDocumentsRefetch?: (
    variables?: GetCaseDocumentsQueryVariables
  ) => Promise<ApolloQueryResult<GetCaseDocumentsQuery>>
  caseSummary?: Document
  caseTasks?: Documents
  caseDataRefetch?: () => void
  incidentRefetch: () => void
  onChange: (args: { sectionTitle: string; index: number }) => void
  onClose: (nextActiveDrawer?: DrawerType) => void
  onLiftSeal: (liftSealParams: LiftSealParamsType) => void
  onRemoveLastSeal: () => void
  onClickAddFile: () => void
  activeCaseTaskData?: ActiveCaseTaskData
  setActiveCaseTaskData?: (newData: ActiveCaseTaskData) => void
  onEditCaseTaskDrawerClose: () => void
  showApproveCaseDispositionDrawer: () => void
  showRejectCaseDispositionDrawer: () => void
  assignCaseDispositionToUser: () => void
  assignCaseDispositionToUserLoading: boolean
}

export function IncidentDrawer({
  activeDrawer,
  activeIndex = 0,
  activeEntityId,
  attachments,
  cfs,
  documents,
  sectionTitle = '',
  isIncidentSealLifted,
  incident,
  caseFriendlyId,
  caseId,
  liftSealParams,
  caseData,
  caseDataRefetch,
  caseDocuments,
  caseDocumentsRefetch,
  caseSummary,
  caseTasks,
  incidentRefetch,
  onChange,
  onClose,
  onLiftSeal,
  onRemoveLastSeal,
  onClickAddFile,
  activeCaseTaskData,
  setActiveCaseTaskData = noop,
  onEditCaseTaskDrawerClose,
  showApproveCaseDispositionDrawer,
  showRejectCaseDispositionDrawer,
  assignCaseDispositionToUser,
  assignCaseDispositionToUserLoading,
}: Props) {
  const shouldShowPropertiesImportedIntoCase = useCanImportPropertyEntitiesIntoCase()
  const shouldShowCaseEntities = !!caseId

  const { agencyConfig, hasAttribute, user } = useUserContext()
  const { access } = useUserAccess([
    UserAccessEnum.AddSealingPermission,
    UserAccessEnum.EditExternalResponseFields,
    UserAccessEnum.LiftSealingPermission,
    UserAccessEnum.RemoveSealingPermission,
    UserAccessEnum.CreateExpungements,
  ])
  const { entityRestrictions: restrictions } = useRestrictions()
  const {
    incidentEntities,
    refetch: refetchIncidentEntities,
    loading: incidentEntitiesLoading,
  } = useQueryIncidentEntities({
    incidentId: incident.id,
    isIncidentSealLifted,
    liftSealParams,
  })

  const isInitialLoadForIncidentEntities = incidentEntitiesLoading && !incidentEntities
  const caseEntityTypesToQuery = shouldShowPropertiesImportedIntoCase
    ? eligibleIncidentCaseEntityTypes
    : eligibleIncidentCaseEntityTypes.filter((t) => t !== EntityEnum.Property)

  const {
    entities: caseEntities,
    refetch: refetchCaseEntities,
    loading: caseEntitiesLoading,
  } = useListIncidentCaseEntitiesQuery({
    caseId: caseId || '',
    skip: !shouldShowCaseEntities || isInitialLoadForIncidentEntities,
    includeDrafts: true,
    entityTypes: caseEntityTypesToQuery,
    originTypes: [EntityEnum.Case],
    includeOnlyLatestVersions: true,
    incidentEntities,
  })

  const { tryTrackCaseTaskAttachEvidence, setActiveAddEvidenceTab } = useTrackCaseTaskAddEvidence()
  const formattedIncidentEntities = formatIncidentEntities({
    incidentEntities,
    restrictions,
  })

  const loading =
    isInitialLoadForIncidentEntities ||
    (shouldShowCaseEntities && caseEntitiesLoading && !caseEntities)

  const entitiesBySectionTitle = React.useMemo(() => {
    if (shouldShowCaseEntities) {
      const formattedCaseEntities = formatIncidentEntities({
        incidentEntities: caseEntities,
        restrictions,
      })
      const mergedGroups = mergeGroupedEntities([formattedIncidentEntities, formattedCaseEntities])
      return getEntitiesBySectionTitleMap(mergedGroups)
    }
    return getEntitiesBySectionTitleMap(formattedIncidentEntities)
  }, [formattedIncidentEntities, shouldShowCaseEntities, restrictions, caseEntities])

  const selectedEntity = React.useMemo(() => {
    const sectionEntities = entitiesBySectionTitle[sectionTitle]

    if (isEmpty(sectionEntities)) {
      return undefined
    }

    return activeEntityId
      ? sectionEntities?.find(
          (entity: FolderEntityBaseDataType<any, any>) => entity.id === activeEntityId
        )
      : sectionEntities?.[activeIndex]
  }, [activeEntityId, activeIndex, entitiesBySectionTitle, sectionTitle])

  const entityType = selectedEntity?.dataType
  const hasInvestigations = hasAttribute(AgencyAttributesEnums.enableInvestigations)
  const refetch = () => {
    incidentRefetch()
    refetchIncidentEntities()
    if (shouldShowCaseEntities) {
      refetchCaseEntities()
    }
  }
  const hasExpungementV2Enabled = hasAttribute(AgencyAttributesEnums.enableExpungementV2)
  const hasCaseTasksEnabled = hasAttribute(AgencyAttributesEnums.enableCaseTasks)
  const hasEnableCaseDispositionReview = hasAttribute(
    AgencyAttributesEnums.enableCaseDispositionReview
  )
  const hasEnableNibrsDispositionReview = hasAttribute(
    AgencyAttributesEnums.enableCaseDispositionUpdateForRecordsWithNIBRSReview
  )
  // const { hasEnableMultiUnitCase } = useMultiUnitCaseFeature()
  // 🚧 TEMPORARY: Hard code feature flag for testing
  const hasEnableMultiUnitCase = true

  const hasInFlight = hasInFlightTask(documents)
  const canExpunge = shouldShowExpungeOption({
    hasExpungementV2Enabled,
    createExpungements: access.createExpungements,
    hasInFlight,
  })
  const onAddCaseTaskClose = (caseTaskData?: ActiveCaseTaskData) => {
    if (caseTaskData) {
      setActiveCaseTaskData(caseTaskData)
      onClose('EditCaseTask')
    } else {
      onClose()
    }
  }
  const restrictionContext = {
    id: incident.id,
    type: RestrictionContextTypeEnum.Incident,
  }

  return (
    <>
      <RestrictionDrawer
        entities={{
          [axonEntityTypes.Incident]: incident.id,
          ...(cfs ? { [axonEntityTypes.CallForService]: cfs.originalEntity.id } : {}),
        }}
        restrictionContext={restrictionContext}
        isOpen={activeDrawer === availableDrawers.Restriction && !loading}
        onClose={onClose}
      />
      <LiftSealDrawer
        isOpen={activeDrawer === availableDrawers.LiftSealing}
        onClose={onClose}
        onSubmit={onLiftSeal}
        sealedEntityFriendlyId={incident.friendlyId || ''}
        sealedEntityType={axonEntityTypes.Incident}
      />
      <NarrativeDrawer
        activeIndex={activeIndex}
        incidentId={incident.id}
        isOpen={activeDrawer === availableDrawers.Narrative}
        isIncidentSealLifted={isIncidentSealLifted}
        liftSealParams={liftSealParams}
        onClose={onClose}
        onChange={(index: number) =>
          onChange({ sectionTitle: availableDrawers.Narrative || '', index })
        }
      />
      {hasCaseTasksEnabled && (
        <AddCaseTaskDrawer
          isOpen={activeDrawer === availableDrawers.AddCaseTask}
          onClose={onAddCaseTaskClose}
          caseId={caseData?.id}
          caseTasksRefetch={caseDocumentsRefetch}
        />
      )}
      {hasCaseTasksEnabled && caseData && activeCaseTaskData?.friendlyId && incident.friendlyId && (
        <EditCaseTaskDrawer
          caseData={caseData}
          isOpen={activeDrawer === availableDrawers.EditCaseTask}
          incidentOrEventFriendlyId={incident.friendlyId}
          friendlyId={activeCaseTaskData.friendlyId}
          onClose={() => {
            onEditCaseTaskDrawerClose()
            onClose()
            caseDocumentsRefetch?.()
            if (shouldShowCaseEntities) {
              refetchCaseEntities()
            }
          }}
          ecomCaseId={incident.ecomCase?.id}
          incidentOrEventId={incident.id}
          onClickAddFile={onClickAddFile}
          onFileDelete={refetch}
          caseTaskEcomEvidenceIds={
            incident.evidence
              ?.filter((evidence) => evidence.reportId === activeCaseTaskData.documentId)
              .map((evidence) => evidence.evidenceId || '') || []
          }
          trackAttachEvidence={tryTrackCaseTaskAttachEvidence}
        />
      )}
      {/* TODO: [RMS-42499] Create dedicated CaseEntityDrawer for case entities */}
      <IncidentEntityDrawer
        documents={
          shouldShowCaseEntities ? [...documents, ...(caseDocuments?.results || [])] : documents
        }
        entity={selectedEntity}
        entitiesBySectionTitle={entitiesBySectionTitle}
        incident={incident}
        incidentEntities={
          shouldShowCaseEntities
            ? [...(incidentEntities || []), ...((caseEntities || []) as IncidentEntity[])]
            : incidentEntities
        }
        sectionTitle={sectionTitle}
        isOpen={
          activeDrawer === availableDrawers.Entity &&
          !!entityType &&
          !!validEntityDrawerTypes[entityType] &&
          !loading
        }
        onChange={onChange}
        onClose={onClose}
      />
      <EvidenceProvider
        entityId={incident.id}
        friendlyId={incident.friendlyId || ''}
        entityType={EntityEnum.Incident}
        ecomCaseId={incident.ecomCase?.id}
        reportId={activeCaseTaskData?.documentId}
      >
        {activeDrawer === availableDrawers.Attachment && (
          <EvidenceUploadDrawer
            title={__('Upload Attachments from Computer')}
            isOpen={activeDrawer === availableDrawers.Attachment}
            onCancel={() => {
              refetch()
              onClose(activeCaseTaskData ? availableDrawers.EditCaseTask : undefined)
            }}
            attachmentCategoryName={agencyConfig.attributes.recordsAttachmentCategoryName ?? ''}
            onClose={() => {
              refetch()
              onClose(activeCaseTaskData ? availableDrawers.EditCaseTask : undefined)
            }}
            onFailure={refetch}
            onSuccess={() => {
              refetch()
              if (activeCaseTaskData) {
                queryClient.invalidateQueries([
                  ecomEndpoints.evidences({ partnerId: user.partnerId || '' }),
                  { caseId: incident.ecomCase?.id },
                ])
              }
            }}
          />
        )}
        {activeDrawer === availableDrawers.AddEvidence && (
          <AddEvidenceDrawer
            isOpen={activeDrawer === availableDrawers.AddEvidence}
            onCancel={() => {
              refetch()
              onClose(activeCaseTaskData ? availableDrawers.EditCaseTask : undefined)
            }}
            attachmentCategoryName={agencyConfig.attributes.recordsAttachmentCategoryName ?? ''}
            onClose={() => {
              refetch()
              onClose(activeCaseTaskData ? availableDrawers.EditCaseTask : undefined)
            }}
            onFailure={refetch}
            onSuccess={refetch}
            onTabChange={setActiveAddEvidenceTab}
            suggestedIds={compact([incident.friendlyId, caseData?.friendlyId, cfs?.id])}
          />
        )}
      </EvidenceProvider>
      <RequestEvidenceDrawer
        incidentId={incident.id}
        incidentFriendlyId={incident.friendlyId || ''}
        isOpen={activeDrawer === availableDrawers.RequestEvidence}
        onClose={onClose}
      />
      <IncidentPrintOptionsDrawer
        isIncidentSealLifted={isIncidentSealLifted}
        incidentId={incident.id || ''}
        caseFriendlyId={caseFriendlyId}
        caseId={caseId}
        incidentFriendlyId={incident.friendlyId || ''}
        isOpen={activeDrawer === availableDrawers.PrintOptions}
        liftSealParams={liftSealParams}
        onClose={onClose}
        caseData={caseData}
      />
      <Sealing
        access={access}
        attachments={attachments}
        cfs={cfs}
        isSealLifted={isIncidentSealLifted}
        isOpen={activeDrawer === availableDrawers.Sealing && !loading}
        scopeInfo={{
          id: incident.id,
          friendlyId: incident.friendlyId,
          incidentDate: incident.incidentDate,
        }}
        liftSealParams={liftSealParams}
        onClose={onClose}
        onRemoveLastSeal={onRemoveLastSeal}
        scope={SealScopeEnums.Incident}
        documents={documents}
      />
      {canExpunge && (
        <ErrorBoundary initiatedFrom={ErrorInitiatedFrom.EXPUNGEMENT}>
          <Expungement
            cfs={cfs}
            incident={incident}
            isOpen={activeDrawer === availableDrawers.Expungement}
            onClose={onClose}
          />
        </ErrorBoundary>
      )}
      <ExternalResponseFieldsDrawer
        editExternalResponseFields={access.editExternalResponseFields || false}
        entities={formattedIncidentEntities}
        incident={incident}
        isOpen={activeDrawer === availableDrawers.ExternalResponseFields && !loading}
        onClose={onClose}
      />
      {hasInvestigations && !caseData && (
        <CaseCreateDrawer
          entityId={incident.id}
          entityType={axonEntityTypes.Incident}
          isOpen={activeDrawer === availableDrawers.CreateCase && !loading}
          onCaseCreate={incidentRefetch}
          onClose={onClose}
        />
      )}
      {caseData && (
        <EditCaseFriendlyNameDrawer
          caseData={caseData}
          caseDataRefetch={caseDataRefetch}
          isOpen={activeDrawer === availableDrawers.EditFriendlyCaseName && !loading}
          onClose={onClose}
          workflowType={WorkflowTypeEnum.Case}
        />
      )}
      {caseData &&
        (hasEnableCaseDispositionReview || hasEnableNibrsDispositionReview ? (
          hasEnableMultiUnitCase ? (
            (function() {
              console.log('🔥 [IncidentDrawer] Rendering DispositionCaseDrawerV3:', {
                isOpen: activeDrawer === availableDrawers.CaseDisposition && !loading,
                activeDrawer,
                loading,
                caseData: caseData ? { id: caseData.id, friendlyId: caseData.friendlyId } : null,
                hasEnableMultiUnitCase,
              })
              
              // ✅ Using original DispositionCaseDrawerV3 with all correct props
              return <DispositionCaseDrawerV3
                isOpen={activeDrawer === availableDrawers.CaseDisposition && !loading}
                caseData={caseData}
                caseDataRefetch={caseDataRefetch}
                onClose={onClose}
                showApproveCaseDispositionDrawer={showApproveCaseDispositionDrawer}
                showRejectCaseDispositionDrawer={showRejectCaseDispositionDrawer}
                assignCaseDispositionToUser={assignCaseDispositionToUser}
                assignCaseDispositionToUserLoading={assignCaseDispositionToUserLoading}
              />
            })()
          ) : (
            <DispositionCaseDrawerV2
              isOpen={activeDrawer === availableDrawers.CaseDisposition && !loading}
              caseData={caseData}
              caseDataRefetch={caseDataRefetch}
              onClose={onClose}
              showApproveCaseDispositionDrawer={showApproveCaseDispositionDrawer}
              showRejectCaseDispositionDrawer={showRejectCaseDispositionDrawer}
              assignCaseDispositionToUser={assignCaseDispositionToUser}
              assignCaseDispositionToUserLoading={assignCaseDispositionToUserLoading}
            />
          )
        ) : (
          <DispositionCaseDrawer
            isOpen={activeDrawer === availableDrawers.CaseDisposition && !loading}
            caseData={caseData}
            caseDataRefetch={caseDataRefetch}
            onClose={onClose}
          />
        ))}
      {caseData && caseData.latestDispositionReviewTask && (
        <>
          <ApproveDispositionDrawer
            isOpen={activeDrawer === availableDrawers.CaseDispositionApproval && !loading}
            dispositionReviewTask={caseData.latestDispositionReviewTask}
            onClose={() => {
              onClose()
              caseDataRefetch?.()
              incidentRefetch()
            }}
          />
          <RejectDispositionDrawer
            isOpen={activeDrawer === availableDrawers.CaseDispositionRejection && !loading}
            dispositionReviewTask={caseData.latestDispositionReviewTask}
            onClose={() => {
              onClose()
              caseDataRefetch?.()
              incidentRefetch()
            }}
          />
          <ReassignDispositionReviewDrawer
            isOpen={activeDrawer === availableDrawers.ReassignCaseDispositionReview && !loading}
            dispositionReviewTask={caseData.latestDispositionReviewTask}
            onClose={() => {
              onClose()
              caseDataRefetch?.()
            }}
          />
        </>
      )}
      {caseData && (
        <AssignDetectiveCaseDrawer
          isOpen={activeDrawer === availableDrawers.ReassignCase && !loading}
          caseData={caseData}
          caseTasks={caseTasks}
          caseSummary={caseSummary}
          onCaseUpdate={() => {
            caseDataRefetch?.()
            caseDocumentsRefetch?.()
          }}
          onClose={onClose}
        />
      )}
      {caseData && (
        <AssignUnitCaseDrawer
          isOpen={activeDrawer === availableDrawers.TransferUnit && !loading}
          caseData={caseData}
          caseSummary={caseSummary}
          caseTasks={caseTasks}
          onCaseUpdate={() => {
            caseDataRefetch?.()
            caseDocumentsRefetch?.()
          }}
          onClose={onClose}
        />
      )}
      {caseData && (
        <CaseNarrativeDrawer
          caseSummary={caseSummary}
          isOpen={activeDrawer === availableDrawers.CaseSummary && !loading}
          onClose={onClose}
          onChange={(index: number) =>
            onChange({ sectionTitle: availableDrawers.CaseSummary || '', index })
          }
        />
      )}
      <RelatedIncidentsEventsDrawer
        isOpen={activeDrawer === availableDrawers.RelatedIncidents && !loading}
        incidentId={incident.id}
        incidentFriendlyId={incident.friendlyId || ''}
        onClose={onClose}
      />
      <RelatedReportDrawer
        isOpen={activeDrawer === availableDrawers.RelatedReport && !loading}
        incidentId={incident.id}
        incidentFriendlyId={incident.friendlyId || ''}
        onClose={onClose}
      />
      <AddActivityLogDrawer
        isOpen={activeDrawer === availableDrawers.ActivityLog && !loading}
        onClose={onClose}
        entityId={incident.id}
        refType={ActivityLogRefTypeEnum.Incident}
        onSave={({ activityType }) => {
          log.track('Activity Logs - Save Log', {
            activityType,
            incidentId: incident.id,
            incidentFriendlyId: incident.friendlyId,
          })
        }}
      />
      {!!incident.ecomCase?.id && (
        <CaseSharingOptionsDrawer
          caseDocuments={caseDocuments}
          caseSummary={caseSummary}
          caseFriendlyId={caseData?.friendlyId}
          ecomCaseId={incident.ecomCase.id}
          isOpen={activeDrawer === availableDrawers.CaseSharing && !loading}
          isIncidentSealLifted={isIncidentSealLifted}
          incidentId={incident.id || ''}
          incidentFriendlyId={incident.friendlyId || ''}
          liftSealParams={liftSealParams}
          onClose={onClose}
          caseData={caseData}
        />
      )}
      <DueDateDrawer
        drawerTitle={__('Update Due Date')}
        refetchQueries={[getActivityLogsGQLName]}
        refetch={caseDataRefetch}
        reportTitle={
          <Text fontStyle="regular16" size="fit" margin={0}>
            {__('Incident $[id]', { id: incident.friendlyId || '' })}
          </Text>
        }
        task={caseData?.task as Task}
        isOpen={activeDrawer === availableDrawers.UpdateDueDate}
        onClose={onClose}
      />
    </>
  )
}
