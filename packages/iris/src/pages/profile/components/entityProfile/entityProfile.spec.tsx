import React from 'react'
import { render, screen } from 'test/utils'
import { createRouteProps } from 'test/utils/routerMocks'
import { createMemoryHistory } from 'history'
import { Router } from 'react-router'
import { mockUseUserAccess, mockUseUserContext } from 'test/mocks/userContext'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import * as useEntityProfileValidationModule from 'store/context/entityProfile/useEntityProfileValidation'
import * as useEntityQueryModule from 'store/context/entityProfile/queries/useEntityQuery'
import * as useFlagsQueryModule from 'shared/queries'
import { OperationVariables, QueryResult } from '@apollo/client'
import { EntityProfile } from './entityProfile'

// avoid console.log statements when running tests
jest.mock('shared/log', () => ({
  log: {
    track: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}))

describe('EntityProfile', () => {
  beforeEach(() => {
    mockUseUserContext({
      overrides: {
        hasAttribute: (attribute: AgencyAttributesEnums) =>
          attribute === AgencyAttributesEnums.enableMultiJuris,
      },
    })

    jest.spyOn(useEntityProfileValidationModule, 'useEntityProfileValidation').mockReturnValue({
      redirectTo: undefined,
    })

    jest.spyOn(useEntityQueryModule, 'useEntityQuery').mockReturnValue({
      originalEntityData: undefined,
      resolvedEntityData: undefined,
      loading: false,
      error: undefined,
      refetch: jest.fn(),
    })

    jest.spyOn(useFlagsQueryModule, 'useFlagsQuery').mockReturnValue({
      data: undefined,
      loading: false,
    } as QueryResult<object, OperationVariables>)

    mockUseUserAccess({ overrides: { viewFlags: true } })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderWithParams = (params) => {
    const routeProps = createRouteProps({ params })
    return render(<EntityProfile {...routeProps} />)
  }

  describe('Component Rendering', () => {
    it.each(['person', 'vehicle', 'organization', 'location'])(
      'should render %s entity profile with correct title and banner',
      (entityType) => {
        const params = {
          agencyId: 'test-agency',
          entityType,
          entityId: 'test-123',
        }

        renderWithParams(params)
        screen.getByText(`Agency test-agency - ${entityType} Profile: test-123`)
        screen.getByText(
          'This profile was created by test-agency and can be viewed but not edited.'
        )
      }
    )
  })

  describe('Redirect Behavior', () => {
    it.each(['unsupported-type', 'Person', 'ORGANIZATION', 'LOCATION'])(
      'should redirect for invalid entity type in the URL: %s',
      (invalidEntityType) => {
        // Mock useEntityProfileValidation to return a redirect for invalid entity types
        jest.spyOn(useEntityProfileValidationModule, 'useEntityProfileValidation').mockReturnValue({
          redirectTo: '/error/404/Unsupported entity type',
        })

        const params = {
          agencyId: 'test-agency',
          entityType: invalidEntityType,
          entityId: 'test-123',
        }

        const { container } = renderWithParams(params)

        // Check that the component doesn't render the expected content (because it redirected)
        expect(container.innerHTML).not.toContain('Agency test-agency')
        expect(container.innerHTML).not.toContain(`${invalidEntityType} Profile`)
      }
    )

    it.each([
      ['all parameters missing', {}],
      ['only agencyId provided', { agencyId: 'test-agency' }],
      ['only entityType provided', { entityType: 'person' }],
      ['only entityId provided', { entityId: 'test-123' }],
      ['agencyId and entityType missing', { entityId: 'test-123' }],
      ['agencyId and entityId missing', { entityType: 'person' }],
      ['entityType and entityId missing', { agencyId: 'test-agency' }],
    ])('should redirect when %s', (_scenario, params) => {
      // Mock useEntityProfileValidation to return a redirect for missing parameters
      jest.spyOn(useEntityProfileValidationModule, 'useEntityProfileValidation').mockReturnValue({
        redirectTo: '/error/404/Missing required parameters',
      })

      const history = createMemoryHistory()

      render(
        <Router history={history}>
          <EntityProfile {...createRouteProps({ params })} />
        </Router>
      )

      expect(history.location.pathname).toContain('/error/404')
    })

    it('should redirect when enableMultiJuris is false', () => {
      mockUseUserContext({
        overrides: {
          hasAttribute: (attribute: AgencyAttributesEnums) =>
            attribute !== AgencyAttributesEnums.enableMultiJuris,
        },
      })

      // Mock useEntityProfileValidation to return a redirect when multi-jurisdiction is disabled
      jest.spyOn(useEntityProfileValidationModule, 'useEntityProfileValidation').mockReturnValue({
        redirectTo: '/error/404/Multi-Jurisdiction is not enabled',
      })

      const params = {
        agencyId: 'test-agency',
        entityType: 'person',
        entityId: 'test-123',
      }

      const history = createMemoryHistory()

      render(
        <Router history={history}>
          <EntityProfile {...createRouteProps({ params })} />
        </Router>
      )

      expect(history.location.pathname).toContain('/error/404')
    })
  })
})
