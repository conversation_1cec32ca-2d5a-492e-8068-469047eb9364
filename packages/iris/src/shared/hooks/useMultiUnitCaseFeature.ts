import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { useUserContext } from 'store/context'

/**
 * Hook to check if the multi-unit case feature is enabled
 * Based on AgencyAttributesEnums.enableMultiUnitCase
 */
export const useMultiUnitCaseFeature = () => {
  const { hasAttribute } = useUserContext()
  
  const hasEnableMultiUnitCase = hasAttribute(AgencyAttributesEnums.enableMultiUnitCase)
  
  return {
    hasEnableMultiUnitCase,
    // Alias for clearer usage
    isMultiUnitCaseEnabled: hasEnableMultiUnitCase,
  }
}
