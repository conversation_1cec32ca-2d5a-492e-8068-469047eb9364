import * as React from 'react'
import { fireEvent, render, screen } from 'test/utils'
import { createMemoryHistory } from 'history'
import { Router } from 'react-router'
import { Button } from '@axon-enterprise/spark'
import { mockUseUserAccess } from 'test/mocks/userContext'
import * as useFlagsQueryModule from 'shared/queries'
import { log } from 'shared/log'
import * as error from 'shared/error'
import { MockApollo } from 'test/mocks/mockApollo'
import { ApolloError, OperationVariables, QueryResult } from '@apollo/client'
import { ProfileProvider, useProfileContext } from './profileContext'
import * as useEntityProfileValidationModule from './useEntityProfileValidation'
import * as useEntityQueryModule from './queries/useEntityQuery'

describe('ProfileContext', () => {
  beforeEach(() => {
    jest.spyOn(useEntityProfileValidationModule, 'useEntityProfileValidation').mockReturnValue({
      redirectTo: undefined,
    })

    jest.spyOn(useEntityQueryModule, 'useEntityQuery').mockReturnValue({
      originalEntityData: undefined,
      resolvedEntityData: undefined,
      loading: false,
      error: undefined,
      refetch: jest.fn(),
    })

    jest.spyOn(useFlagsQueryModule, 'useFlagsQuery').mockReturnValue({
      data: undefined,
      loading: false,
    } as QueryResult<object, OperationVariables>)

    mockUseUserAccess({ overrides: { viewFlags: true } })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const initialProps = { agencyId: 'agency1', entityType: 'person', friendlyId: '123' }

  it('should provide the context to its child component', () => {
    setup()

    screen.getByText('Test Component')
    screen.getByText('entity data: undefined')
    screen.getByText('entity loading: false')
    screen.getByText('entity error: undefined')
    screen.getByText('entity type: undefined')
    screen.getByText('flags count: 0')
    screen.getByText('flags loading: false')
    screen.getByText('friendly id: undefined')
    screen.getByText('view schema: undefined')
    screen.getByText('refetch entity: function')
  })

  it.each([
    {
      testName: 'entity type',
      initialText: 'entity type: undefined',
      buttonText: 'Update Entity Type',
      expectedText: 'entity type: person',
    },
    {
      testName: 'friendly id',
      initialText: 'friendly id: undefined',
      buttonText: 'Update Friendly ID',
      expectedText: 'friendly id: test-friendly-123',
    },
  ])('should update $testName', ({ initialText, buttonText, expectedText }) => {
    setup()

    screen.getByText(initialText)

    fireEvent.click(screen.getByText(buttonText))

    screen.getByText(expectedText)
  })

  it('should reset all values when resetContext function is called', () => {
    setup()

    // Set some values first
    fireEvent.click(screen.getByText('Update Entity Type'))
    fireEvent.click(screen.getByText('Update Friendly ID'))

    // Verify values are set
    screen.getByText('entity type: person')
    screen.getByText('friendly id: test-friendly-123')

    // Reset all values
    fireEvent.click(screen.getByText('Reset All'))

    // Verify values are reset
    screen.getByText('entity type: undefined')
    screen.getByText('friendly id: undefined')
  })

  it('should redirect to error page when entity query fails', () => {
    const history = createMemoryHistory()
    jest.spyOn(log, 'error').mockImplementation(() => {})
    jest.spyOn(error, 'onError').mockImplementation(() => {})
    jest.spyOn(useEntityQueryModule, 'useEntityQuery').mockReturnValue({
      originalEntityData: undefined,
      resolvedEntityData: undefined,
      loading: false,
      error: { message: 'Entity not found' } as ApolloError,
      refetch: jest.fn(),
    })

    render(
      <MockApollo>
        <Router history={history}>
          <ProfileProvider entityType="person" friendlyId="123" agencyId="agency1">
            <div>Test Component</div>
          </ProfileProvider>
        </Router>
      </MockApollo>
    )

    // Should not render the test component due to redirect
    expect(screen.queryByText('Test Component')).not.toBeInTheDocument()
    expect(log.error).toHaveBeenCalledWith('Entity query failed for person 123: Entity not found')
    expect(error.onError).toHaveBeenCalledWith(500, 'Failed to load entity data')
    expect(history.location.pathname).toBe(
      '/records/error/500/Failed to load entity data. Please try again.'
    )
  })

  // Test resetContext when props change (url changes)
  it.each([
    {
      testName: 'agencyId prop changes',
      newProps: { agencyId: 'agency2', entityType: 'person', friendlyId: '123' },
      expectedEntityType: 'person',
      expectedFriendlyId: '123',
    },
    {
      testName: 'entityType prop changes',
      newProps: { agencyId: 'agency1', entityType: 'vehicle', friendlyId: '123' },
      expectedEntityType: 'vehicle',
      expectedFriendlyId: '123',
    },
    {
      testName: 'friendlyId prop changes',
      newProps: { agencyId: 'agency1', entityType: 'person', friendlyId: '456' },
      expectedEntityType: 'person',
      expectedFriendlyId: '456',
    },
    {
      testName: 'multiple props change simultaneously',
      newProps: { agencyId: 'agency2', entityType: 'vehicle', friendlyId: '456' },
      expectedEntityType: 'vehicle',
      expectedFriendlyId: '456',
    },
    {
      testName: 'undefined prop values',
      newProps: { agencyId: undefined, entityType: undefined, friendlyId: undefined },
      expectedEntityType: 'undefined',
      expectedFriendlyId: 'undefined',
    },
  ])(
    'should reset context when $testName',
    ({ newProps, expectedEntityType, expectedFriendlyId }) => {
      const { rerender } = render(
        <ProfileProvider {...initialProps}>
          <TestComponent />
        </ProfileProvider>
      )

      // Initial state should show the props
      screen.getByText(`entity type: ${initialProps.entityType}`)
      screen.getByText(`friendly id: ${initialProps.friendlyId}`)

      rerender(
        <ProfileProvider {...newProps}>
          <TestComponent />
        </ProfileProvider>
      )

      // After prop change, context should be reset with new prop values
      screen.getByText(`entity type: ${expectedEntityType}`)
      screen.getByText(`friendly id: ${expectedFriendlyId}`)
    }
  )
})

function TestComponent() {
  const {
    entityData,
    entityLoading,
    entityError,
    entityType,
    flags,
    flagsLoading,
    friendlyId,
    viewSchema,
    refetchEntity,
    updateContext,
    resetContext,
  } = useProfileContext()

  const handleUpdateEntityType = () => {
    updateContext({
      entityType: 'person',
    })
  }

  const handleUpdateFriendlyId = () => {
    updateContext({
      friendlyId: 'test-friendly-123',
    })
  }

  return (
    <>
      <div>Test Component</div>
      <div>{`entity data: ${entityData ? JSON.stringify(entityData) : 'undefined'}`}</div>
      <div>{`entity loading: ${entityLoading}`}</div>
      <div>{`entity error: ${entityError ? JSON.stringify(entityError) : 'undefined'}`}</div>
      <div>{`entity type: ${entityType || 'undefined'}`}</div>
      <div>{`flags count: ${flags?.length || 0}`}</div>
      <div>{`flags loading: ${flagsLoading}`}</div>
      {flags?.length && (
        <div>{`flags: ${flags.map((flag) => flag.flagCategory?.categoryName).join(', ')}`}</div>
      )}
      <div>{`friendly id: ${friendlyId || 'undefined'}`}</div>
      <div>{`view schema: ${viewSchema ? JSON.stringify(viewSchema) : 'undefined'}`}</div>
      <div>{`refetch entity: ${
        typeof refetchEntity === 'function' ? 'function' : 'undefined'
      }`}</div>
      <Button text="Update Entity Type" onClick={handleUpdateEntityType} />
      <Button text="Update Friendly ID" onClick={handleUpdateFriendlyId} />
      <Button text="Reset All" onClick={() => resetContext()} />
    </>
  )
}

function setup() {
  return render(
    <ProfileProvider>
      <TestComponent />
    </ProfileProvider>
  )
}
