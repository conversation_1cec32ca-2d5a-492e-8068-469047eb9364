import React, { ReactElement, useCallback, useState } from 'react'
import { ApolloError } from 'store/graphql/apollo'
import { Redirect } from 'react-router-dom'
import { EntityData } from 'shared/entity'
import { Flag, RmsModuleTypeEnum, UserAccessEnum, ViewSchema } from 'static/graphql/types'
import { log } from 'shared/log'
import { onError } from 'shared/error'
import { useFlagsQuery } from 'shared/queries'
import { useUserAccess } from 'shared/hooks'
import { routes } from 'shared/routes'
import { useEntityProfileValidation } from './useEntityProfileValidation'
import { useEntityQuery } from './queries/useEntityQuery'

export interface ProfileContextType {
  entityData?: EntityData
  entityLoading: boolean
  entityError?: ApolloError
  entityType?: string
  flags?: Flag[]
  flagsLoading: boolean
  friendlyId?: string
  viewSchema?: ViewSchema
  partnerAgencyId?: string
  updateContext: (updates: Partial<ProfileContextType>) => void
  resetContext: () => void
  refetchEntity: () => void
}

export const initialContextValue: ProfileContextType = {
  entityData: undefined,
  entityLoading: false,
  entityError: undefined,
  entityType: undefined,
  flags: [],
  flagsLoading: false,
  friendlyId: undefined,
  viewSchema: undefined,
  partnerAgencyId: undefined,
  updateContext: () => {},
  resetContext: () => {},
  refetchEntity: () => {},
}

interface ProfileProviderProps {
  children: ReactElement
  friendlyId?: string
  agencyId?: string
  entityType?: string
}

export const ProfileContext = React.createContext(initialContextValue)
export const useProfileContext = (): ProfileContextType => React.useContext(ProfileContext)

export const ProfileProvider = ({
  children,
  friendlyId,
  agencyId,
  entityType,
}: ProfileProviderProps) => {
  const [contextState, setContextState] = useState<
    Omit<ProfileContextType, 'updateContext' | 'resetContext' | 'refetchEntity'>
  >({
    ...initialContextValue,
    entityType,
    friendlyId,
    partnerAgencyId: agencyId,
  })

  const {
    access: { viewFlags: canViewFlags },
  } = useUserAccess([UserAccessEnum.ViewFlags])

  const {
    originalEntityData,
    resolvedEntityData,
    loading: entityLoading,
    error: entityError,
    refetch,
  } = useEntityQuery({
    variables: {
      friendlyId,
      entityType,
    },
    skip: !friendlyId || !entityType,
  })

  const { data: flagsData, loading: flagsLoading } = useFlagsQuery({
    skip: !friendlyId || !canViewFlags || !entityType || !originalEntityData,
    variables: {
      filters: { submodule: RmsModuleTypeEnum.Records, entityId: originalEntityData?.id || '' },
    },
  })

  const resetContext = useCallback(
    (updatedFriendlyId?: string, updatedEntityType?: string, updatedAgencyId?: string) => {
      setContextState({
        ...initialContextValue,
        entityType: updatedEntityType || entityType,
        friendlyId: updatedFriendlyId || friendlyId,
        partnerAgencyId: updatedAgencyId || agencyId,
      })
    },
    [agencyId, entityType, friendlyId]
  )

  // Reset context when key props change (navigation between entities)
  React.useEffect(() => {
    resetContext(friendlyId, entityType, agencyId)
  }, [agencyId, entityType, friendlyId, resetContext])

  const { redirectTo } = useEntityProfileValidation({ agencyId, entityType, friendlyId })

  if (redirectTo) {
    return <Redirect to={redirectTo} />
  }

  // Handle entity query errors
  if (entityError) {
    log.error(`Entity query failed for ${entityType} ${friendlyId}: ${entityError.message}`)
    onError(500, 'Failed to load entity data')
    return (
      <Redirect
        to={routes.ERROR.route({
          code: '500',
          message: __('Failed to load entity data. Please try again.'),
        })}
      />
    )
  }

  const updateContext = (updates: Partial<ProfileContextType>) => {
    setContextState((prev) => ({ ...prev, ...updates }))
  }

  const contextValue: ProfileContextType = {
    ...contextState,
    entityData: resolvedEntityData,
    entityLoading,
    entityError,
    flags: flagsData?.flags?.results,
    flagsLoading,
    updateContext,
    resetContext,
    refetchEntity: refetch || (() => {}),
    viewSchema: originalEntityData?.viewSchemas?.complete?.schema,
  }

  return <ProfileContext.Provider value={contextValue}>{children}</ProfileContext.Provider>
}
