import { renderHook } from 'test/utils'
import { OperationVariables, QueryResult, useQuery } from 'store/graphql/apollo'
import { useLatestEntityEnums } from 'shared/queries'
import { useEntityQuery } from './useEntityQuery'
import { getResolvedEntityData } from './util'

// Mock the external dependencies
jest.mock('store/graphql/apollo')
jest.mock('shared/queries')
jest.mock('./util')

const mockUseQuery = useQuery as jest.MockedFunction<typeof useQuery>
const mockUseLatestEntityEnums = useLatestEntityEnums as jest.MockedFunction<
  typeof useLatestEntityEnums
>
const mockGetResolvedEntityData = getResolvedEntityData as jest.MockedFunction<
  typeof getResolvedEntityData
>

describe('useEntityQuery', () => {
  const mockFriendlyId = 'test-id-123'
  const mockEntityData = { id: 'entity-1', name: 'Test Entity' }
  const mockLatestEntityEnums = [
    {
      title: 'TestEnum',
      dataRef: 'testRef',
      options: [{ value: 'Value1' }],
    },
  ]
  const mockResolvedEntityData = { id: 'entity-1', name: 'Test Entity', processed: true }

  beforeEach(() => {
    jest.clearAllMocks()

    mockUseQuery.mockReturnValue({
      data: undefined,
      loading: false,
      error: undefined,
      refetch: jest.fn(),
    } as unknown as QueryResult<object, OperationVariables>)

    mockUseLatestEntityEnums.mockReturnValue({
      data: mockLatestEntityEnums,
      loading: false,
    })

    mockGetResolvedEntityData.mockReturnValue(mockResolvedEntityData)
  })

  it.each([
    {
      entityType: 'person',
      expectedVariables: {
        id: mockFriendlyId,
        queryByFriendlyId: true,
        skipBookingsQuery: true,
      },
    },
    {
      entityType: 'organization',
      expectedVariables: {
        id: mockFriendlyId,
        descending: false,
        fromLatestSupplement: true,
      },
    },
    {
      entityType: 'vehicle',
      expectedVariables: {
        id: mockFriendlyId,
      },
    },
    {
      entityType: 'location',
      expectedVariables: {
        id: mockFriendlyId,
      },
    },
  ])(
    'should call the correct GraphQL query and return data for $entityType',
    ({ entityType, expectedVariables }) => {
      const mockData = { [entityType]: mockEntityData }
      mockUseQuery.mockReturnValue({
        data: mockData,
        loading: false,
        error: undefined,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      const { result } = renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType,
          },
        })
      )

      expect(mockUseQuery).toHaveBeenCalledWithGqlVariables({
        variables: expectedVariables,
        skip: false,
        fetchPolicy: 'cache-first',
      })
      expect(result.current.originalEntityData).toEqual(mockEntityData)
      expect(result.current.resolvedEntityData).toEqual(mockResolvedEntityData)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBeUndefined()
    }
  )

  it.each([
    {
      description: 'when entityType is not supported',
      options: {
        variables: {
          friendlyId: mockFriendlyId,
          entityType: 'UnsupportedEntityType',
        },
      },
    },
    {
      description: 'when entityType is undefined',
      options: {
        variables: {
          friendlyId: mockFriendlyId,
          entityType: undefined,
        },
      },
    },
    {
      description: 'when skip option is true',
      options: {
        variables: {
          friendlyId: mockFriendlyId,
          entityType: 'person',
        },
        skip: true,
      },
    },
  ])('should skip the query $description', ({ options }) => {
    renderHook(() => useEntityQuery(options))

    expect(mockUseQuery).toHaveBeenCalledWith(undefined, {
      variables: undefined,
      skip: true,
      fetchPolicy: 'cache-first',
    })
  })

  it.each([
    {
      description: 'when query is loading',
      setupMocks: () => {
        mockUseQuery.mockReturnValue({
          data: undefined,
          loading: true,
          error: undefined,
          refetch: jest.fn(),
        } as unknown as QueryResult<object, OperationVariables>)
      },
      expectedLoading: true,
    },
    {
      description: 'when entity enums are loading',
      setupMocks: () => {
        mockUseLatestEntityEnums.mockReturnValue({
          data: mockLatestEntityEnums,
          loading: true,
        })
      },
      expectedLoading: true,
    },
    {
      description: 'when both queries are not loading',
      setupMocks: () => {
        mockUseQuery.mockReturnValue({
          data: { person: mockEntityData },
          loading: false,
          error: undefined,
          refetch: jest.fn(),
        } as unknown as QueryResult<object, OperationVariables>)

        mockUseLatestEntityEnums.mockReturnValue({
          data: mockLatestEntityEnums,
          loading: false,
        })
      },
      expectedLoading: false,
    },
  ])('should return loading $expectedLoading $description', ({ setupMocks, expectedLoading }) => {
    setupMocks()

    const { result } = renderHook(() =>
      useEntityQuery({
        variables: {
          friendlyId: mockFriendlyId,
          entityType: 'person',
        },
      })
    )

    expect(result.current.loading).toBe(expectedLoading)
  })

  describe('error handling', () => {
    it('should return error when query fails', () => {
      const mockError = new Error('Query failed')
      mockUseQuery.mockReturnValue({
        data: undefined,
        loading: false,
        error: mockError,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      // Mock getResolvedEntityData to return undefined when there's an error
      mockGetResolvedEntityData.mockReturnValue(undefined)

      const { result } = renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'person',
          },
        })
      )

      expect(result.current.error).toEqual(mockError)
      expect(result.current.originalEntityData).toBeUndefined()
      expect(result.current.resolvedEntityData).toBeUndefined()
    })
  })

  describe('data processing', () => {
    it('should call getResolvedEntityData with correct parameters', () => {
      const mockData = { person: mockEntityData }
      mockUseQuery.mockReturnValue({
        data: mockData,
        loading: false,
        error: undefined,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'person',
          },
        })
      )

      expect(mockGetResolvedEntityData).toHaveBeenCalledWith({
        entityData: mockEntityData,
        entityType: 'person',
        latestEntityEnums: mockLatestEntityEnums,
      })
    })

    it('should not call getResolvedEntityData when data is undefined', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        loading: false,
        error: undefined,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'person',
          },
        })
      )

      expect(mockGetResolvedEntityData).toHaveBeenCalledWith({
        entityData: undefined,
        entityType: 'person',
        latestEntityEnums: mockLatestEntityEnums,
      })
    })
  })

  describe('useLatestEntityEnums integration', () => {
    it('should call useLatestEntityEnums with correct entityType', () => {
      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'organization',
          },
        })
      )

      expect(mockUseLatestEntityEnums).toHaveBeenCalledWith({
        entityType: 'organization',
        skip: false,
      })
    })

    it('should handle undefined entityType in useLatestEntityEnums', () => {
      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: undefined,
          },
        })
      )

      expect(mockUseLatestEntityEnums).toHaveBeenCalledWith({
        entityType: undefined,
        skip: true,
      })
    })
  })

  describe('skip parameter handling', () => {
    it('should pass skip parameter to useQuery when provided', () => {
      const mockData = { person: mockEntityData }
      mockUseQuery.mockReturnValue({
        data: mockData,
        loading: false,
        error: undefined,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'person',
          },
          skip: true,
        })
      )

      expect(mockUseQuery).toHaveBeenCalledWithGqlVariables({
        variables: undefined, // When skip is true, queryConfig becomes null, so variables becomes undefined
        skip: true,
        fetchPolicy: 'cache-first',
      })
    })

    it('should default skip to false when not provided', () => {
      const mockData = { person: mockEntityData }
      mockUseQuery.mockReturnValue({
        data: mockData,
        loading: false,
        error: undefined,
        refetch: jest.fn(),
      } as unknown as QueryResult<object, OperationVariables>)

      renderHook(() =>
        useEntityQuery({
          variables: {
            friendlyId: mockFriendlyId,
            entityType: 'person',
          },
        })
      )

      expect(mockUseQuery).toHaveBeenCalledWithGqlVariables({
        variables: {
          id: mockFriendlyId,
          queryByFriendlyId: true,
          skipBookingsQuery: true,
        },
        skip: false,
        fetchPolicy: 'cache-first',
      })
    })
  })
})
