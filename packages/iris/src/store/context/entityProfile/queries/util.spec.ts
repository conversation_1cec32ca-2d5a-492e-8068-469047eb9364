import { compact } from 'lodash'
import { EntityEnumInfo, EntityTypeEnum } from 'static/graphql/types'
import * as schemautils from '@axon-enterprise/schemautils'
import * as sharedUtils from 'shared/utils'
import * as sharedMni from 'shared/mni'
import { getVehicleEntity } from 'test/mocks/vehicleEntity'
import { getLocation } from 'test/mocks/location'
import { getPersonEntity } from 'test/mocks/personEntity'
import { getOrganizationEntity } from 'test/mocks/organizationEntity'
import { getResolvedEntityData } from './util'

describe('getResolvedEntityData', () => {
  const mockLatestEntityEnums: EntityEnumInfo[] = [
    {
      title: 'TestEnum',
      dataRef: 'testRef',
      options: [{ value: 'Value1' }],
    },
  ]

  const mockEntityEnums = [{ title: 'TestEnum', dataRef: 'testRef', options: [] }]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('when entityData is undefined', () => {
    it('should return undefined for undefined entityData', () => {
      const result = getResolvedEntityData({
        entityData: undefined,
        entityType: EntityTypeEnum.Person,
        latestEntityEnums: mockLatestEntityEnums,
      })

      expect(result).toBeUndefined()
    })
  })

  describe('when entityType is Vehicle or Location', () => {
    it.each([
      {
        entityType: EntityTypeEnum.Vehicle,
        getMockEntity: getVehicleEntity,
        description:
          'should process vehicle data with entityEnumInfosToEntityEnums and replaceEnumsFromEntityPayload',
      },
      {
        entityType: EntityTypeEnum.Location,
        getMockEntity: getLocation,
        description:
          'should process location data with entityEnumInfosToEntityEnums and replaceEnumsFromEntityPayload',
      },
    ])('$description', ({ entityType, getMockEntity }) => {
      const mockEntityData = getMockEntity()
      const mockEntityEnumInfosToEntityEnums = jest
        .spyOn(sharedUtils, 'entityEnumInfosToEntityEnums')
        .mockReturnValue(mockEntityEnums)
      const mockReplaceEnumsFromEntityPayload = jest
        .spyOn(schemautils, 'replaceEnumsFromEntityPayload')
        .mockReturnValue(mockEntityData.data)

      const result = getResolvedEntityData({
        entityData: mockEntityData,
        entityType,
        latestEntityEnums: mockLatestEntityEnums,
      })

      expect(mockEntityEnumInfosToEntityEnums).toHaveBeenCalledWith(mockLatestEntityEnums)
      expect(mockReplaceEnumsFromEntityPayload).toHaveBeenCalledWith({
        entityPayload: mockEntityData,
        entityEnums: mockEntityEnums,
      })
      expect(result).toEqual(mockEntityData.data)
    })
  })

  describe('Organization entity type', () => {
    const mockLocation = getLocation()
    const mockOrganizationData = getOrganizationEntity({
      overrides: {
        relatedIncidentEntities: {
          locations: {
            results: [{ location: mockLocation }],
          },
        },
      },
    })
    const mockProcessedLocation = compact(
      (mockOrganizationData.relatedIncidentEntities?.locations?.results || []).map(
        (relatedLocation) => relatedLocation.location
      )
    )

    it('should process organization data with related locations', () => {
      const mockProcessedData = {
        id: 'org-1',
        name: 'Test Organization',
        relatedLocations: [
          { id: 'loc-1', name: 'Related Location' },
          { id: 'loc-2', name: 'Another Location' },
        ],
      }

      jest.spyOn(sharedUtils, 'entityEnumInfosToEntityEnums').mockReturnValue(mockEntityEnums)
      const mockGetEntityDataWithRelatedEntities = jest
        .spyOn(schemautils, 'getEntityDataWithRelatedEntities')
        .mockReturnValue(mockProcessedData)
      const mockReplaceEnumsFromEntityPayload = jest
        .spyOn(schemautils, 'replaceEnumsFromEntityPayload')
        .mockReturnValue(mockProcessedData)

      const result = getResolvedEntityData({
        entityData: mockOrganizationData,
        entityType: EntityTypeEnum.Organization,
        latestEntityEnums: mockLatestEntityEnums,
      })

      expect(mockGetEntityDataWithRelatedEntities).toHaveBeenCalledWith({
        originalEntityData: mockOrganizationData.data,
        relatedEntities: mockProcessedLocation,
        relatedEntityPath: '@rels/OrganizationToLocation/*/@otherEntity',
      })

      expect(mockReplaceEnumsFromEntityPayload).toHaveBeenCalledWith({
        entityPayload: mockProcessedData,
        entityEnums: mockEntityEnums,
      })

      expect(result).toEqual(mockProcessedData)
    })
  })

  describe('Person entity type', () => {
    const mockPersonData = getPersonEntity()
    it('should process person data correctly', () => {
      const mockDenormalizedData = {
        id: 'person-1',
        ...mockPersonData.data,
      }
      const mockResolvedEntityData = {
        id: 'person-1',
        ...mockDenormalizedData,
      }

      jest.spyOn(sharedUtils, 'entityEnumInfosToEntityEnums').mockReturnValue(mockEntityEnums)
      const mockGetDenormalizePayload = jest
        .spyOn(sharedMni, 'getDenormalizePayload')
        .mockReturnValue(mockDenormalizedData)
      const mockReplaceEnumsFromEntityPayload = jest
        .spyOn(schemautils, 'replaceEnumsFromEntityPayload')
        .mockReturnValue(mockResolvedEntityData)

      const result = getResolvedEntityData({
        entityData: mockPersonData,
        entityType: EntityTypeEnum.Person,
        latestEntityEnums: mockLatestEntityEnums,
      })

      expect(mockGetDenormalizePayload).toHaveBeenCalledWith(
        mockPersonData.data,
        mockPersonData.dataSchema?.entityDataRefs || []
      )

      expect(mockReplaceEnumsFromEntityPayload).toHaveBeenCalledWith({
        entityPayload: mockDenormalizedData,
        entityEnums: mockEntityEnums,
      })

      expect(result).toEqual(mockResolvedEntityData)
    })
  })
})
