import React from 'react'
import { AgencyAttributesEnums } from 'shared/agencyAttributes'
import { log } from 'shared/log'
import { routes } from 'shared/routes'
import { EntityTypeEnum } from 'static/graphql/types'
import { useUserContext } from '../user'

export enum MissingParamEnum {
  AGENCY_ID = 'agencyId',
  ENTITY_TYPE = 'entityType',
  FRIENDLY_ID = 'friendlyId',
}
interface EntityProfileParams {
  agencyId?: string
  entityType?: string
  friendlyId?: string
}

interface ValidationResult {
  redirectTo?: string
}

// enums are all uppercase, so we need to convert to lowercase for url comparison
export const supportedEntityTypes = new Set(
  [
    EntityTypeEnum.Person,
    EntityTypeEnum.Vehicle,
    EntityTypeEnum.Organization,
    EntityTypeEnum.Location,
  ].map((entityTypeEnum) => entityTypeEnum.toLowerCase())
)

export function useEntityProfileValidation(params: EntityProfileParams): ValidationResult {
  const { hasAttribute } = useUserContext()
  const isMultiJurisEnabled = hasAttribute(AgencyAttributesEnums.enableMultiJuris)

  const { agencyId, entityType, friendlyId } = params

  React.useEffect(() => {
    const warnMessages: string[] = []

    if (!isMultiJurisEnabled) {
      warnMessages.push('Multi-Jurisdiction is not enabled')
    }
    if (!agencyId || !entityType || !friendlyId) {
      warnMessages.push('Missing required parameters')
    }
    if (entityType && !supportedEntityTypes.has(entityType)) {
      warnMessages.push('User opened Entity Profile with unsupported entity type')
    }
    const warnMessage = warnMessages.length > 0 ? warnMessages.join('; ') : undefined

    if (warnMessage) {
      log.warn(`Entity Profile Page - ${warnMessage}`, {
        data: {
          agencyId,
          entityType,
          friendlyId,
        },
      })
      return
    }

    // TODO: RMS-87342 replace with Mixpanel events
    log.track('Entity Profile Page - User opened Entity Profile', {
      agencyId,
      entityType,
      friendlyId,
    })
  }, [agencyId, entityType, friendlyId, isMultiJurisEnabled])

  if (!isMultiJurisEnabled) {
    return {
      redirectTo: routes.ERROR.route({
        code: '404',
        message: __('Error Code 404: Multi-Jurisdiction is not enabled'),
      }),
    }
  }

  if (!agencyId || !entityType || !friendlyId) {
    const missingParams: MissingParamEnum[] = []
    if (!agencyId) {
      missingParams.push(MissingParamEnum.AGENCY_ID)
    }
    if (!entityType) {
      missingParams.push(MissingParamEnum.ENTITY_TYPE)
    }
    if (!friendlyId) {
      missingParams.push(MissingParamEnum.FRIENDLY_ID)
    }

    return {
      redirectTo: routes.ERROR.route({
        code: '404',
        message: __('Error Code 404: Missing required parameters: $[missingParams]', {
          missingParams: missingParams.join(', '),
        }),
      }),
    }
  }

  if (!supportedEntityTypes.has(entityType)) {
    return {
      redirectTo: routes.ERROR.route({
        code: '404',
        message: __('Error Code 404: Unsupported entity type: $[entityType]', {
          entityType,
        }),
      }),
    }
  }

  return {
    redirectTo: undefined,
  }
}
