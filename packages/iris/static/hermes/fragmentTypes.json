{"__schema": {"types": [{"kind": "UNION", "name": "CaseOrIncidentEntity", "description": null, "specifiedByURL": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "CaseEntity", "ofType": null}, {"kind": "OBJECT", "name": "IncidentEntity", "ofType": null}]}, {"kind": "UNION", "name": "DynamicSearchResultUnion", "description": null, "specifiedByURL": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "BookingResult", "ofType": null}, {"kind": "OBJECT", "name": "CallForServiceResult", "ofType": null}, {"kind": "OBJECT", "name": "ChargeResult", "ofType": null}, {"kind": "OBJECT", "name": "ENoteResult", "ofType": null}, {"kind": "OBJECT", "name": "EventResult", "ofType": null}, {"kind": "OBJECT", "name": "EvidenceFileR<PERSON>ult", "ofType": null}, {"kind": "OBJECT", "name": "IncidentResult", "ofType": null}, {"kind": "OBJECT", "name": "LocationResult", "ofType": null}, {"kind": "OBJECT", "name": "OrganizationResult", "ofType": null}, {"kind": "OBJECT", "name": "PepContainerResult", "ofType": null}, {"kind": "OBJECT", "name": "PermitResult", "ofType": null}, {"kind": "OBJECT", "name": "PersonResult", "ofType": null}, {"kind": "OBJECT", "name": "PropertyResult", "ofType": null}, {"kind": "OBJECT", "name": "SuggestLocationResult", "ofType": null}, {"kind": "OBJECT", "name": "TaskSearchResult", "ofType": null}, {"kind": "OBJECT", "name": "TrainingCourseResult", "ofType": null}, {"kind": "OBJECT", "name": "TrainingCurriculumResult", "ofType": null}, {"kind": "OBJECT", "name": "User", "ofType": null}, {"kind": "OBJECT", "name": "VehicleResult", "ofType": null}, {"kind": "OBJECT", "name": "WarrantResult", "ofType": null}]}, {"kind": "UNION", "name": "SchemaInfo", "description": null, "specifiedByURL": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "LabelGroupEntityInfo", "ofType": null}, {"kind": "OBJECT", "name": "LabelGroupFormInfo", "ofType": null}, {"kind": "OBJECT", "name": "LabelGroupSubDocumentInfo", "ofType": null}]}, {"kind": "UNION", "name": "StandardsCaseOrEventEntity", "description": null, "specifiedByURL": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "StandardsCaseEntity", "ofType": null}, {"kind": "OBJECT", "name": "EventEntity", "ofType": null}]}, {"kind": "UNION", "name": "TaskItem", "description": null, "specifiedByURL": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "Booking", "ofType": null}, {"kind": "OBJECT", "name": "Case", "ofType": null}, {"kind": "OBJECT", "name": "DispositionReview", "ofType": null}, {"kind": "OBJECT", "name": "Document", "ofType": null}, {"kind": "OBJECT", "name": "StandardsCase", "ofType": null}, {"kind": "OBJECT", "name": "User", "ofType": null}]}]}}